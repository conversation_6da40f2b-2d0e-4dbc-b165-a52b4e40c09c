;ELC   
;;; Compiled
;;; in Emacs version 29.3
;;; with all optimizations.



(byte-code "\300\301!\210\3021 \300\303!0\210\202 \210\3041 \300\305!0\210\202 \210\3061* \300\307!0\210\202+ \210\31017 \300\311!0\210\2028 \210\3121D \300\313!0\210\202E \210\3141Q \300\315!0\210\202R \210\3161^ \300\317!0\210\202_ \210\3201k \300\321!0\210\202l \210\3221x \300\323!0\210\202y \210\3241\205 \300\325!0\210\202\206 \210\3261\222 \300\327!0\210\202\223 \210\3301\237 \300\331!0\210\202\240 \210\3321\254 \300\333!0\210\202\255 \210\3341\266 \300\335!0\207\210\336\207" [require cl-lib (error) shared-context (error) context (error) core/backend (error) customization/prompts (error) chat-customization (error) chat-sessions (error) tools/tools-core (error) agents/agents-core (error) chat-mode (error) ui/markdown-renderer (error) ui/streaming (error) ui/message-actions (error) ui/collapsible (error) ui/themes nil] 2)#@58 Whether the enhanced UI features are enabled by default.
(defvar ai-auto-complete-enhanced-ui-enabled t (#$ . 924))
(byte-code "\300\301!\204\f \302\301\303\304#\210\300\305!\204 \302\305\306\307#\210\300\310!\204$ \302\310\311\312#\210\300\313!\2044 \302\313\314\315#\210\316\313!\210\300\317!\204D \302\317\314\320#\210\316\317!\210\300\321!\204T \302\321\314\322#\210\316\321!\210\300\323!\204d \302\323\314\324#\210\316\323!\210\300\325!\204t \302\325\314\326#\210\316\325!\210\314\207" [boundp ai-auto-complete-chat-buffer-name defvar-1 "*AI Auto Complete Chat*" "Name of the chat buffer." ai-auto-complete-chat-prompt-prefix "USER: " "Prefix for user messages in the chat interface." ai-auto-complete-backend gemini "Default backend to use for AI Auto Complete." ai-auto-complete--chat-history nil "History of messages in the chat buffer." make-variable-buffer-local ai-auto-complete--chat-in-progress "Flag to track if a chat request is in progress." ai-auto-complete--chat-input-marker "Marker for the current input position in the chat buffer." ai-auto-complete--current-session-id "ID of the current chat session." ai-auto-complete--session-modified "Flag to track if the current session has been modified."] 4)#@51 Marker for the header section in the chat buffer.
(defvar ai-auto-complete--chat-header-marker nil (#$ . 2152))
(make-variable-buffer-local 'ai-auto-complete--chat-header-marker)#@52 Marker for the content section in the chat buffer.
(defvar ai-auto-complete--chat-content-marker nil (#$ . 2336))
(make-variable-buffer-local 'ai-auto-complete--chat-content-marker)#@51 The currently active agent in the chat interface.
(defvar ai-auto-complete--chat-active-agent nil (#$ . 2523))
(make-variable-buffer-local 'ai-auto-complete--chat-active-agent)#@51 Whether streaming is enabled in this chat buffer.
(defvar ai-auto-complete-chat-streaming-enabled t (#$ . 2705))
(make-variable-buffer-local 'ai-auto-complete-chat-streaming-enabled)#@60 Whether markdown rendering is enabled in this chat buffer.
(defvar ai-auto-complete-chat-markdown-enabled t (#$ . 2893))
(make-variable-buffer-local 'ai-auto-complete-chat-markdown-enabled)#@49 Whether to show timestamps in this chat buffer.
(defvar ai-auto-complete-chat-show-timestamps t (#$ . 3088))
(make-variable-buffer-local 'ai-auto-complete-chat-show-timestamps)#@51 Whether to show tool calls in the chat interface.
(defvar ai-auto-complete--chat-show-tool-calls t (#$ . 3270))#@53 Whether to show tool results in the chat interface.
(defvar ai-auto-complete--chat-show-tool-results t (#$ . 3387))#@71 Whether message actions (edit, copy, delete, regenerate) are enabled.
(defvar ai-auto-complete-message-actions-enabled t (#$ . 3508))#@123 Hook run after a user sends a message in the chat interface.
The hook function receives the message text as its argument.
(defvar ai-auto-complete-chat-message-hook nil (#$ . 3648))#@43 Timeout in seconds for chat API requests.
(defvar ai-auto-complete-chat-request-timeout 30 (#$ . 3835))#@42 Timer for tracking API request timeouts.
(defvar ai-auto-complete--chat-timeout-timer nil (#$ . 3944))#@55 Whether control buttons are shown in the chat header.
(defvar ai-auto-complete-chat-show-control-buttons t (#$ . 4052))#@32 Keymap for enhanced chat mode.
(defvar ai-auto-complete-enhanced-chat-mode-map (byte-code "\301 \302\303\304#\210\302\305\306#\210\302\307\310#\210\302\311\312#\210\302\313\314#\210\302\315\316#\210\302\317\320#\210\302\321\322#\210\302\323\324#\210\302\325\326#\210\302\327\330#\210\302\317\331#\210\302\321\332#\210\302\333\334#\210\302\307\335#\210)\207" [map make-sparse-keymap define-key "" ai-auto-complete-chat-toggle-timestamps "" ai-auto-complete-chat-toggle-message-actions "" ai-auto-complete-chat-select-agent "" ai-auto-complete-chat-clear "\f" ai-auto-complete-chat-load-session-action "" ai-auto-complete-chat-save-session-action "" ai-auto-complete-chat-refresh "" ai-auto-complete-chat-scroll-to-bottom "" ai-auto-complete-chat-scroll-to-top "" ai-auto-complete-chat-next-message "" ai-auto-complete-chat-previous-message ai-auto-complete-chat-toggle-tool-results ai-auto-complete-chat-toggle-sidebar "
" ai-auto-complete-chat-toggle-markdown ai-auto-complete-chat-toggle-streaming] 4) (#$ . 4177))
(custom-declare-face 'ai-auto-complete-enhanced-chat-timestamp-face '((t :foreground "#888a85" :slant italic :height 0.8)) "Face for timestamps in the enhanced chat interface." :group 'ai-auto-complete-chat)
(custom-declare-face 'ai-auto-complete-enhanced-chat-tool-face '((t :foreground "#ad7fa8" :weight bold)) "Face for tool calls in the enhanced chat interface." :group 'ai-auto-complete-chat)
(custom-declare-face 'ai-auto-complete-enhanced-chat-tool-result-face '((t :foreground "#729fcf" :weight bold)) "Face for tool results in the enhanced chat interface." :group 'ai-auto-complete-chat)
(custom-declare-face 'ai-auto-complete-enhanced-chat-agent-face '((t :foreground "#fcaf3e" :weight bold)) "Face for agent messages in the enhanced chat interface." :group 'ai-auto-complete-chat)
(custom-declare-face 'ai-auto-complete-enhanced-chat-button-face '((t :box t :foreground "#729fcf" :background "#eeeeec")) "Face for buttons in the enhanced chat interface." :group 'ai-auto-complete-chat)
(custom-declare-face 'ai-auto-complete-enhanced-chat-header-face '((t :height 1.2 :weight bold :foreground "#3465a4" :background "#eeeeec")) "Face for the header in the enhanced chat interface." :group 'ai-auto-complete-chat)
(custom-declare-face 'ai-auto-complete-enhanced-chat-user-bubble-face '((t :background "#e3f2fd" :foreground "#1976d2" :box (:line-width 1 :color "#bbdefb" :style nil))) "Face for user message bubbles in the enhanced chat interface." :group 'ai-auto-complete-chat)
(custom-declare-face 'ai-auto-complete-enhanced-chat-assistant-bubble-face '((t :background "#f3e5f5" :foreground "#7b1fa2" :box (:line-width 1 :color "#ce93d8" :style nil))) "Face for assistant message bubbles in the enhanced chat interface." :group 'ai-auto-complete-chat)
(custom-declare-face 'ai-auto-complete-enhanced-chat-agent-bubble-face '((t :background "#fff3e0" :foreground "#f57c00" :box (:line-width 1 :color "#ffcc02" :style nil))) "Face for agent message bubbles in the enhanced chat interface." :group 'ai-auto-complete-chat)
(custom-declare-face 'ai-auto-complete-enhanced-chat-tool-bubble-face '((t :background "#e8f5e8" :foreground "#388e3c" :box (:line-width 1 :color "#a5d6a7" :style nil))) "Face for tool message bubbles in the enhanced chat interface." :group 'ai-auto-complete-chat)
(custom-declare-face 'ai-auto-complete-enhanced-chat-separator-face '((t :foreground "#bdbdbd" :height 0.8)) "Face for message separators in the enhanced chat interface." :group 'ai-auto-complete-chat)
(byte-code "\300\301!\204 \302\301\303\304\305\306%\210\300\307!\204 \302\307\310\311\305\306%\210\300\312!\204* \302\312\313\314\305\306%\210\300\315!\2048 \302\315\316\317\305\306%\210\320\321!\204C \322\321\323\"\210\324\325\326\"\207" [facep ai-auto-complete-user-face custom-declare-face ((t :foreground "#729fcf" :weight bold)) "Face for user messages in chat." :group ai-auto-complete-chat ai-auto-complete-assistant-face ((t :foreground "#8ae234" :weight bold)) "Face for assistant messages in chat." ai-auto-complete-system-face ((t :foreground "#eeeeec" :slant italic)) "Face for system messages in chat." ai-auto-complete-agent-face ((t :foreground "#fcaf3e" :weight bold)) "Face for agent messages in chat." fboundp string-empty-p defalias #[(string) "?\206 \301\230\207" [string ""] 2 "Return t if STRING is empty or nil."] add-hook ai-auto-complete-chat-message-hook ai-auto-complete-chat-send-to-backend] 6)#@56 Initialize the chat buffer with all enhanced features.
(defalias 'ai-auto-complete-chat-initialize #[nil "\306\307 \210\310\311\312\313\314\306\315\306\316\306&	c\210\310\317\320!\312\321\314\306\315\306\316\306&	c\210	\203* \322 \210\323 \310\324\314\306\315\306\316\306&c\210\323 \325\304!\203I \f\203I \326 \210\310\327\312\330\314\306\315\306\316\306&	c\210\310
\312\331\314\306\315\306\316\306&	c\210\323 db\210\332``\314\333$\210\333\211)\207" [inhibit-read-only ai-auto-complete-chat-show-control-buttons ai-auto-complete--chat-header-marker ai-auto-complete--chat-content-marker ai-auto-complete--chat-history ai-auto-complete-chat-prompt-prefix t erase-buffer propertize "AI Auto Complete Chat\n" face ai-auto-complete-enhanced-chat-header-face read-only front-sticky rear-nonsticky format-time-string "Started on %Y-%m-%d %H:%M:%S\n" ai-auto-complete-enhanced-chat-timestamp-face ai-auto-complete-chat-insert-control-buttons point-marker "\n" boundp ai-auto-complete-chat-display-conversation "Type your message below and press Enter to send.\n\n" ai-auto-complete-system-face ai-auto-complete-user-face put-text-property nil ai-auto-complete--chat-input-marker buffer-read-only] 10 (#$ . 8662)])#@33 Check if we can send a message.
(defalias 'ai-auto-complete-chat-can-send-p #[nil "?\205 	\205 \302	!\205 `	Y\207" [ai-auto-complete--chat-in-progress ai-auto-complete--chat-input-marker marker-position] 2 (#$ . 9882)])#@29 Get the current input text.
(defalias 'ai-auto-complete-chat-get-input-text #[nil "\205 \301!\205 \302d\"\207" [ai-auto-complete--chat-input-marker marker-position buffer-substring-no-properties] 3 (#$ . 10111)])#@41 Send INPUT-TEXT with streaming enabled.
(defalias 'ai-auto-complete-chat-send-with-streaming #[(input-text) "\301!\207" [input-text ai-auto-complete-chat-send-traditional] 2 (#$ . 10334)])#@43 Send INPUT-TEXT using traditional method.
(defalias 'ai-auto-complete-chat-send-traditional #[(input-text) "\205U \304!\211\305\230\262?\205U \306\307\"\210\310B	B\nd|\210db\210\311\312\313#\314\315\316\317\320\317\321\317&	c\210\311\322\314\323\316\317\320\317\321\317&	c\210\311\314\315\316\317\320\317\321\317&	c\210\324 db\207" [input-text ai-auto-complete--chat-history ai-auto-complete--chat-input-marker ai-auto-complete-chat-prompt-prefix string-trim "" message "Sending message: %s" user propertize format "\n%s%s\n\n" face ai-auto-complete-user-face read-only t front-sticky rear-nonsticky "AI: Thinking...\n\n" ai-auto-complete-assistant-face point-marker] 10 (#$ . 10529)])#@21 Insert a timestamp.
(defalias 'ai-auto-complete-chat-insert-timestamp #[nil "\300\301\302!\303\304\305\306\307\306\310\306&	c\207" [propertize format-time-string "[%H:%M:%S] " face ai-auto-complete-enhanced-chat-timestamp-face read-only t front-sticky rear-nonsticky] 10 (#$ . 11230)])#@30 Insert role prefix for ROLE.
(defalias 'ai-auto-complete-chat-insert-role-prefix #[(role) "\302\267\202 \303\202 \304\202 \305\202 \306\202 \307\202 \310\311	\312\313\267\2029 \314\202: \315\202: \316\202: \317\202: \320\202: \321\322\323\324\323\325\323&	c)\207" [role prefix #s(hash-table size 5 test eq rehash-size 1.5 rehash-threshold 0.8125 purecopy t data (user 6 assistant 10 agent 14 tool 18 tool-result 22)) "USER: " "AI: " "AGENT: " "TOOL: " "RESULT: " "UNKNOWN: " propertize face #s(hash-table size 5 test eq rehash-size 1.5 rehash-threshold 0.8125 purecopy t data (user 37 assistant 41 agent 45 tool 49 tool-result 53)) ai-auto-complete-user-face ai-auto-complete-assistant-face ai-auto-complete-enhanced-chat-agent-face ai-auto-complete-enhanced-chat-tool-face ai-auto-complete-enhanced-chat-tool-result-face default read-only t front-sticky rear-nonsticky] 10 (#$ . 11521)])#@41 Insert CONTENT with markdown rendering.
(defalias 'ai-auto-complete-chat-insert-markdown-content #[(content) "\301\302!\203 \303\302!\304\305\306\305\307\305&c\207\310!\207" [content fboundp ai-auto-complete-markdown-render propertize read-only t front-sticky rear-nonsticky ai-auto-complete-chat-insert-plain-content] 8 (#$ . 12424)])#@31 Insert CONTENT as plain text.
(defalias 'ai-auto-complete-chat-insert-plain-content #[(content) "\301\302\303\304\303\305\303&c\207" [content propertize read-only t front-sticky rear-nonsticky] 8 (#$ . 12769)])#@29 Insert a message separator.
(defalias 'ai-auto-complete-chat-insert-separator #[nil "\300\301\302\303\304\303\305\303&c\207" [propertize "\n\n" read-only t front-sticky rear-nonsticky] 8 (#$ . 12987)])#@36 Send input with streaming support.
(defalias 'ai-auto-complete-chat-send-input #[nil "\302 \205 \303 	\203 \304!\202 \305!)\207" [input-text ai-auto-complete-chat-streaming-enabled ai-auto-complete-chat-can-send-p ai-auto-complete-chat-get-input-text ai-auto-complete-chat-send-with-streaming ai-auto-complete-chat-send-traditional] 2 (#$ . 13195) nil])#@44 Insert message with all enhanced features.
(defalias 'ai-auto-complete-chat-insert-message #[(message role &optional metadata) "\306	\203	 \307 \210\310\n!\210\203 \311\f!\210\202 \312\f!\210
\203% \313\n\f\"\210\314 )\207" [inhibit-read-only ai-auto-complete-chat-show-timestamps role ai-auto-complete-chat-markdown-enabled message ai-auto-complete-message-actions-enabled t ai-auto-complete-chat-insert-timestamp ai-auto-complete-chat-insert-role-prefix ai-auto-complete-chat-insert-markdown-content ai-auto-complete-chat-insert-plain-content ai-auto-complete-message-actions-insert-buttons ai-auto-complete-chat-insert-separator] 3 (#$ . 13560)])#@46 Toggle streaming in the current chat buffer.
(defalias 'ai-auto-complete-chat-toggle-streaming #[nil "?\301\302\203
 \303\202 \304\"\207" [ai-auto-complete-chat-streaming-enabled message "Streaming %s" "enabled" "disabled"] 3 (#$ . 14219) nil])#@55 Toggle markdown rendering in the current chat buffer.
(defalias 'ai-auto-complete-chat-toggle-markdown #[nil "?\301 \210\302\303\203 \304\202 \305\"\207" [ai-auto-complete-chat-markdown-enabled ai-auto-complete-chat-refresh message "Markdown rendering %s" "enabled" "disabled"] 3 (#$ . 14473) nil])#@54 Start or switch to the AI auto complete chat buffer.
(defalias 'ai-auto-complete-chat #[nil "\306\307\310\300!\203\f \202
 \311\"\210\310\301!\203 	\203 \306\312!\210\306\313\n\"\210\314!r\fq\210
\315=\2043 \315 \210\204J \316\317!\210\306\320\203G \321\202H \322\"\210\204b \203b \323\324!\210\204b \325\326!\327 \210\326\203t \330!\204{ db\210\331 )\332\f!\210\205\217 \330!\205\217 b)\207" [ai-auto-complete-chat-backend ai-auto-complete-tools-enabled ai-auto-complete-backend ai-auto-complete-chat-buffer-name chat-buffer major-mode message "Starting chat function with backend: %s" boundp undefined "Tools are enabled for chat mode" "Set chat backend to %s" get-buffer-create text-mode ai-auto-complete-chat-mode 1 "Chat mode after enabling: %s" "enabled" "disabled" call-interactively ai-auto-complete-chat-session-new ai-auto-complete-chat--generate-session-id nil ai-auto-complete-chat-initialize marker-position point-marker switch-to-buffer ai-auto-complete--current-session-id ai-auto-complete-session-ask-on-new buffer-read-only ai-auto-complete--chat-input-marker] 4 (#$ . 14782) nil])#@53 Insert enhanced control buttons in the chat header.
(defalias 'ai-auto-complete-chat-insert-control-buttons #[nil "\305\306\307\310\305\311\305\312\305&c\210\313\314\315\316\317\320\321\305\322\323&	\210\306\324\310\305\311\305\312\305&c\210\313\325\315\316\317\326\321\305\322\327&	\210\306\324\310\305\311\305\312\305&c\210\313\330\315\316\317\331\321\305\322\332&	\210\306\324\310\305\311\305\312\305&c\210\313\333\315\316\317\334\321\305\322\335&	\210\306\324\310\305\311\305\312\305&c\210\313	\203{ \336\202| \337\315\316\317\340\321\305\322\341&	\210\306\324\310\305\311\305\312\305&c\210\313\n\203\234 \342\202\235 \343\315\316\317\344\321\305\322\345&	\210\306\324\310\305\311\305\312\305&c\210\313\203\275 \346\202\276 \347\315\316\317\350\321\305\322\351&	\210\306\324\310\305\311\305\312\305&c\210\313\f\203\336 \352\202\337 \353\315\316\317\354\321\305\322\355&	\210\306\356\310\305\311\305\312\305&c)\207" [inhibit-read-only ai-auto-complete-chat-show-timestamps ai-auto-complete-message-actions-enabled ai-auto-complete-chat-markdown-enabled ai-auto-complete-chat-streaming-enabled t propertize "[ " read-only front-sticky rear-nonsticky insert-text-button "Select Agent" face ai-auto-complete-enhanced-chat-button-face action ai-auto-complete-chat-select-agent-action follow-link help-echo "Select an agent to chat with" " | " "Clear Chat" ai-auto-complete-chat-clear-action "Clear the chat history" "Save Session" ai-auto-complete-chat-save-session-action "Save the current chat session" "Load Session" ai-auto-complete-chat-load-session-action "Load a saved chat session" "Hide Timestamps" "Show Timestamps" ai-auto-complete-chat-toggle-timestamps-action "Toggle display of timestamps" "Hide Actions" "Show Actions" ai-auto-complete-chat-toggle-message-actions-action "Toggle message action buttons" "Hide Markdown" "Show Markdown" ai-auto-complete-chat-toggle-markdown-action "Toggle markdown rendering" "Hide Streaming" "Show Streaming" ai-auto-complete-chat-toggle-streaming-action "Toggle streaming responses" " ]\n"] 10 (#$ . 15917)])#@37 Action for the select agent button.
(defalias 'ai-auto-complete-chat-select-agent-action #[(button) "\300 \207" [ai-auto-complete-chat-select-agent] 1 (#$ . 17990)])#@35 Action for the clear chat button.
(defalias 'ai-auto-complete-chat-clear-action #[(button) "\300 \207" [ai-auto-complete-chat-clear] 1 (#$ . 18161)])#@37 Action for the save session button.
(defalias 'ai-auto-complete-chat-save-session-action #[(button) "\300\301!\205	 \302\301!\207" [fboundp ai-auto-complete-chat-session-save call-interactively] 2 (#$ . 18316)])#@37 Action for the load session button.
(defalias 'ai-auto-complete-chat-load-session-action #[(button) "\300\301!\205	 \302\301!\207" [fboundp ai-auto-complete-chat-session-load call-interactively] 2 (#$ . 18533)])#@42 Action for the toggle timestamps button.
(defalias 'ai-auto-complete-chat-toggle-timestamps-action #[(button) "\300 \207" [ai-auto-complete-chat-toggle-timestamps] 1 (#$ . 18750)])#@47 Action for the toggle message actions button.
(defalias 'ai-auto-complete-chat-toggle-message-actions-action #[(button) "\300 \207" [ai-auto-complete-chat-toggle-message-actions] 1 (#$ . 18936)])#@40 Action for the toggle markdown button.
(defalias 'ai-auto-complete-chat-toggle-markdown-action #[(button) "\300 \207" [ai-auto-complete-chat-toggle-markdown] 1 (#$ . 19137)])#@41 Action for the toggle streaming button.
(defalias 'ai-auto-complete-chat-toggle-streaming-action #[(button) "\300 \207" [ai-auto-complete-chat-toggle-streaming] 1 (#$ . 19317)])#@31 Select an agent to chat with.
(defalias 'ai-auto-complete-chat-select-agent #[nil "\305\300!\2052 \2052 \305\301!\2052 \306	!\2052 \307	!\310\311\n\312\313$\211\211\314\230\262?\2051 \315\316\"*\207" [ai-auto-complete-agents-enabled ai-auto-complete-agents agents agent-name ai-auto-complete--chat-active-agent boundp hash-table-p hash-table-keys completing-read "Select agent: " nil t "" message "Selected agent: %s"] 6 (#$ . 19500) nil])#@31 Toggle display of timestamps.
(defalias 'ai-auto-complete-chat-toggle-timestamps #[nil "?\301\302\203
 \303\202 \304\"\210\305 \207" [ai-auto-complete-chat-show-timestamps message "Timestamps %s" "enabled" "disabled" ai-auto-complete-chat-refresh] 3 (#$ . 19952) nil])#@43 Toggle display of message action buttons.
(defalias 'ai-auto-complete-chat-toggle-message-actions #[nil "?\301\302\203
 \303\202 \304\"\210\305 \207" [ai-auto-complete-message-actions-enabled message "Message actions %s" "enabled" "disabled" ai-auto-complete-chat-refresh] 3 (#$ . 20230) nil])#@51 Refresh the chat interface with current settings.
(defalias 'ai-auto-complete-chat-refresh #[nil "\304!\205( rq\210\305	\205 \306	!\205 \307	d\"\310 \210\n\205' 	b\210\nc+\207" [ai-auto-complete-chat-buffer-name ai-auto-complete--chat-input-marker input-text inhibit-read-only get-buffer t marker-position buffer-substring-no-properties ai-auto-complete-chat-initialize] 4 (#$ . 20533)])#@71 Display the current conversation history in the enhanced chat buffer.
(defalias 'ai-auto-complete-chat-display-conversation #[nil "\306\300!\205\313\205\313\307\nb\210\n\203 \310!\203 \202 d|\210\311\312!
\205\312
@\211'@'A()*\203H \313)(\f#\210\202\277)\314\267\202\207+\203d \315\316\317!\320\321\322\307\323\307\324\307&	c\210\315\325\320\326\322\307\323\307\324\307&	c\210\315(\322\307\323\307\324\307\327)\330\f&c\210\202\207+\203\234 \315\316\317!\320\321\322\307\323\307\324\307&	c\210\315\331\320\332\322\307\323\307\324\307&	c\210\315(\322\307\323\307\324\307\327)\330\f&c\210\202\207+\203\324 \315\316\317!\320\321\322\307\323\307\324\307&	c\210(@(A,-\315\333\334-\"\320\335\322\307\323\307\324\307&	c\210\315,\322\307\323\307\324\307\327)\330\f&c*\210\202\207.\203 +\203 \315\316\317!\320\321\322\307\323\307\324\307&	c\210.\203\207\315\336\320\337\322\307\323\307\324\307&	c\210\315(\322\307\323\307\324\307\327)\330\f&c\210\202\207/\203b+\203b\315\316\317!\320\321\322\307\323\307\324\307&	c\210/\203\207\315\340\320\341\322\307\323\307\324\307&	c\210\315(\322\307\323\307\324\307\327)\330\f&c\210\342\343!\203\2630\203\263)\344>\203\263)\345=\203\246(A\202\250(1\343)1\f#)\210\315\346\322\307\323\307\324\307&c\210\fT*
A)\202' +\207" [ai-auto-complete--chat-history inhibit-read-only ai-auto-complete--chat-content-marker ai-auto-complete--chat-input-marker index #1=#:tail boundp t marker-position 0 reverse ai-auto-complete-chat-render-message-bubble #s(hash-table size 5 test eq rehash-size 1.5 rehash-threshold 0.8125 purecopy t data (user 79 assistant 135 agent 191 tool 262 tool-result 328)) propertize format-time-string "[%H:%M:%S] " face ai-auto-complete-enhanced-chat-timestamp-face read-only front-sticky rear-nonsticky "USER: " ai-auto-complete-user-face ai-auto-complete-message-role ai-auto-complete-message-index "AI: " ai-auto-complete-assistant-face format "AGENT-%s: " ai-auto-complete-agent-face "TOOL: " font-lock-function-name-face "RESULT: " font-lock-doc-face fboundp ai-auto-complete-message-actions-insert-buttons (user assistant agent) agent "\n\n" msg content role ai-auto-complete-chat-markdown-enabled ai-auto-complete-chat-show-timestamps agent-content agent-name ai-auto-complete--chat-show-tool-calls ai-auto-complete--chat-show-tool-results ai-auto-complete-message-actions-enabled msg-content] 13 (#$ . 20932)])#@71 Render a message with ROLE and CONTENT at INDEX using bubble styling.
(defalias 'ai-auto-complete-chat-render-message-bubble #[(role content index) "\306\307\267\202 \310\202 \311\202 \312\202 \313\202 \313\202 \314\315\267\202C \316\202D \317\202D \320\321	:\2036 	@\2027 \322\"\202D \323\202D \324\202D \322\325=\203T 	:\203T 	A\202U 	:\203n \326\327\330!\331\332\333\306\334\306\335\306&	c\210\326\320\336\"\331\337\267\202\216 \340\202\217 \341\202\217 \342\202\217 \343\202\217 \344\202\217 \314\333\306\334\306\335\306&	c\210;\203\244 \345\n!\202\245 \n<\326\346\333\306\334\306\335\306&c\210;\203\347\350!\203\351<\"\203\320\352\353!\226\354<\355\"G#\350=\326<\331\f\333\306\334\306\335\306\356\357>&
\360=\203\362 \360\202\361<\362\363\306$\266\203\203\364\202\301\365$?=\326?\333\306\334\306\335\306&c*\210\2021\326<\331\f\333\306\334\306\335\306\356\357>&
c\210\326\346\333\306\334\306\335\306&c)\210@\203V\347\366!\203V\367>\203V\366\n>#\210\326\355\333\306\334\306\335\306&c\210\326\370\331\371\333\306\334\306\335\306&	c\210\326\355\333\306\334\306\335\306&c,\207" [role content message-content role-label bubble-face inhibit-read-only t #s(hash-table size 5 test eq rehash-size 1.5 rehash-threshold 0.8125 purecopy t data (user 7 assistant 11 agent 15 tool 19 tool-result 23)) ai-auto-complete-enhanced-chat-user-bubble-face ai-auto-complete-enhanced-chat-assistant-bubble-face ai-auto-complete-enhanced-chat-agent-bubble-face ai-auto-complete-enhanced-chat-tool-bubble-face default #s(hash-table size 5 test eq rehash-size 1.5 rehash-threshold 0.8125 purecopy t data (user 34 assistant 38 agent 42 tool 59 tool-result 63)) "YOU" "AI" format "AGENT-%s" "UNKNOWN" "TOOL" "RESULT" agent propertize format-time-string "[%H:%M:%S] " face ai-auto-complete-enhanced-chat-timestamp-face read-only front-sticky rear-nonsticky "%s: " #s(hash-table size 5 test eq rehash-size 1.5 rehash-threshold 0.8125 purecopy t data (user 122 assistant 126 agent 130 tool 134 tool-result 138)) ai-auto-complete-user-face ai-auto-complete-assistant-face ai-auto-complete-enhanced-chat-agent-face ai-auto-complete-enhanced-chat-tool-face ai-auto-complete-enhanced-chat-tool-result-face ai-auto-complete-markdown-render "\n  " fboundp ai-auto-complete-collapsible-create ai-auto-complete-chat-should-collapse-content "%s Content (%d lines)" symbol-name split-string "\n" ai-auto-complete-message-role ai-auto-complete-message-index tool-result "```" nil string-match code-block collapsed ai-auto-complete-message-actions-insert-buttons (user assistant agent) "────────────────────────────────────────────────────────────────\n" ai-auto-complete-enhanced-chat-separator-face ai-auto-complete-chat-show-timestamps ai-auto-complete-chat-markdown-enabled rendered-content title index collapsible-content ai-auto-complete-message-actions-enabled] 17 (#$ . 23376)])#@53 Determine if CONTENT with ROLE should be collapsed.
(defalias 'ai-auto-complete-chat-should-collapse-content #[(content role) "\304\305\"GG\211\306V\2062 	\307V\2062 \310=\203! \n\311V\2062 \312\313\314\315$\266\203\2052 \n\316V*\207" [content char-count line-count role split-string "\n" 15 1000 tool-result 5 "```" nil string-match t 10] 9 (#$ . 26420)])#@48 Smoothly scroll the chat buffer to the bottom.
(defalias 'ai-auto-complete-chat-scroll-to-bottom #[nil "\306!\205A rq\210\307p!\211\n\205@ \n@\310!r\311\216\312\f@\313\"\210db\210\314\315!\210
\2038 \316
!\2038 
b\210+\nA)\202 +\207" [ai-auto-complete-chat-buffer-name windows #1=#:tail window save-selected-window--state ai-auto-complete--chat-input-marker get-buffer get-buffer-window-list internal--before-with-selected-window #[nil "\301!\207" [save-selected-window--state internal--after-with-selected-window] 2] select-window norecord recenter -1 marker-position] 4 (#$ . 26791)])#@41 Auto-scroll during streaming responses.
(defalias 'ai-auto-complete-chat-auto-scroll #[nil "\205\n 	\205\n \302 \207" [ai-auto-complete-chat-streaming-enabled ai-auto-complete--chat-in-progress ai-auto-complete-chat-scroll-to-bottom] 1 (#$ . 27393)])#@39 Scroll to the top of the chat buffer.
(defalias 'ai-auto-complete-chat-scroll-to-top #[nil "\301!\205 rq\210eb\210\302\303!)\207" [ai-auto-complete-chat-buffer-name get-buffer recenter 0] 2 (#$ . 27650) nil])#@43 Navigate to the next message in the chat.
(defalias 'ai-auto-complete-chat-next-message #[nil "\301`\302\"\211\205 b\210\303 )\207" [next-pos next-single-property-change ai-auto-complete-message-role recenter] 4 (#$ . 27867) nil])#@47 Navigate to the previous message in the chat.
(defalias 'ai-auto-complete-chat-previous-message #[nil "\301`\302\"\211\205 b\210\303 )\207" [prev-pos previous-single-property-change ai-auto-complete-message-role recenter] 4 (#$ . 28106) nil])#@39 Handle timeout for chat API requests.
(defalias 'ai-auto-complete-chat-timeout-handler #[nil "\205 \303\304	\"\210\305\306!\210\307\211\207" [ai-auto-complete--chat-in-progress ai-auto-complete-chat-request-timeout ai-auto-complete--chat-timeout-timer message "API request timed out after %d seconds" ai-auto-complete-chat-handle-response "ERROR: Request timed out. The API did not respond within the expected time. Please try again or switch to a different backend." nil] 3 (#$ . 28357)])#@65 Send INPUT-TEXT to the current backend and handle the response.
(defalias 'ai-auto-complete-chat-send-to-backend #[(input-text) "\306\307\310\311G^O\"\210\312\301!\2032 	\2032 \313\314!\2032 \313\315!\2032 \314\316\317\"\2042 \315\317\320\316#\210\306\321!\210\322 \203= \323!\210\324\f\325\326#
\"\306\327\"\"\210\"\330\267\202 \306\331!\210\332\n\333\"\202\200 \306\334!\210\335\n\333\"\202\200 \306\336!\210\337\n\333\"\202\200 \306\340!\210\341\n\333\"\202\200 \325*\207" [input-text ai-auto-complete-tools-enabled prompt ai-auto-complete--chat-timeout-timer ai-auto-complete-chat-request-timeout ai-auto-complete-backend message "Starting ai-auto-complete-chat-send-to-backend with input: %s" 0 20 boundp fboundp advice-member-p advice-add ai-auto-complete-tools-advice-request ai-auto-complete-complete :around "Added tools advice to ai-auto-complete-complete" ai-auto-complete-chat-build-prompt cancel-timer run-with-timer nil ai-auto-complete-chat-timeout-handler "DEBUG: Using backend for request: %s" #s(hash-table size 4 test eq rehash-size 1.5 rehash-threshold 0.8125 purecopy t data (gemini 83 openai 94 anthropic 105 openrouter 116)) "DEBUG: Calling Gemini provider" ai-auto-complete--gemini-complete ai-auto-complete-chat-handle-response-with-cleanup "DEBUG: Calling OpenAI provider" ai-auto-complete--openai-complete "DEBUG: Calling Anthropic provider" ai-auto-complete--anthropic-complete "DEBUG: Calling OpenRouter provider" ai-auto-complete--openrouter-complete backend] 6 (#$ . 28855)])#@50 Handle RESPONSE from the AI and clean up timers.
(defalias 'ai-auto-complete-chat-handle-response-with-cleanup #[(response) "\302\303\203 \304\305G^O\202 \306\"\210\302\307\310!\"\210\302\311;\"\210\302\312;\2052 \313\314\315\316$\266\203\"\210	\203> \317	!\210\314\320!\207" [response ai-auto-complete--chat-timeout-timer message "Response handler called with response: %s" 0 50 "nil" "Response type: %s" type-of "Response is string: %s" "Response is error: %s" "^ERROR:" nil string-match t cancel-timer ai-auto-complete-chat-handle-response] 10 (#$ . 30377)])#@58 Build a prompt from the chat history and shared context.
(defalias 'ai-auto-complete-chat-build-prompt #[nil "\306\307\203 \310\311G\"\202 \312\"\210\306\313\314\301!\203! 	\203! \315\202\" \316\"\210\n\317!\306\320\f\"\321*\322\323!\203= \323 \202> \321+\321,\324\325!-\".\326*.//\203\236 /@\2110@0A12*2\327\267\202\200 \330\202\201 \310\3311@\"\202\201 \332\202\201 \3332\334=\203\216 1A\202\220 1\335R**/A/)\202S *\314\336!\203\266 \203\266 \322\337!\203\266 \340\337 P,\306\341!\210\306\342+G\"\210\306\343*G\"\210\306\344,G\"\210+\211\321\230\262\204\364 \306\345+G+G\346V\203\360 +\347\346O\350P\202\362 +#\210,\211\321\230\262\204\306\351,G\"\210\f+*,R.\207" [ai-auto-complete--chat-history ai-auto-complete-chat-mode ai-auto-complete-backend backend system-prompt dummy message "Chat history length: %s" format "%d messages" "empty" "Chat mode active: %s" boundp "yes" "no" ai-auto-complete-get-system-prompt "Chat using system prompt: %s" "" fboundp ai-auto-complete-get-context-for-prompt seq-take reverse "\n\nConversation history:\n" #s(hash-table size 3 test eq rehash-size 1.5 rehash-threshold 0.8125 purecopy t data (user 111 agent 115 tool-result 124)) "User: " "Agent %s: " "Tool Results: " "Assistant: " agent "\n\n" ai-auto-complete-tools-enabled ai-auto-complete-get-tool-definitions "\n\nTool definitions:\n" "Chat prompt components:" "  Shared context length: %d" "  History text length: %d" "  Tool definitions length: %d" "Chat mode: Shared context found (length: %d). Preview: %s" 100 0 "..." "Chat mode: Tool definitions added (length: %d)" history-text shared-context tool-definitions ai-auto-complete-chat-max-history history #1=#:tail msg content role] 7 (#$ . 30958)])#@49 Handle RESPONSE from the AI in the chat buffer.
(defalias 'ai-auto-complete-chat-handle-response #[(response) "\306!\203\276 \307\310	\206
 \311\"\210r\306!q\210\312db\210\313 d|\210	\204B \314\315\316\317\320\312\321\312\322\312&	c\210\314\323\316\324\320\312\321\312\322\312&	c\210\202\226 	;\203t \325	\326\327\312$\266\203\203t \314\315\316\317\320\312\321\312\322\312&	c\210\314	\316\324\320\312\321\312\322\312&	c\210\202\226 \330	BB\312\314
\316\331\320\312\321\312\322\312&	c\210\314	\320\312\321\312\322\312&c\210\314\332\320\312\321\312\322\312&c\210\314\316\333\320\312\321\312\322\312&	c\210\334 \326\211 \211!*\207\307\335!\207" [ai-auto-complete-chat-buffer-name response inhibit-read-only ai-auto-complete--chat-history ai-auto-complete--session-modified ai-auto-complete-chat-response-prefix get-buffer message "Received response: %s" "nil" t line-beginning-position propertize "emacs: " face ai-auto-complete-system-face read-only front-sticky rear-nonsticky "ERROR: Failed to get a response from the API. Please try again or switch to a different backend." error "^ERROR:" nil string-match assistant ai-auto-complete-assistant-face "\n\n" ai-auto-complete-user-face point-marker "Chat buffer not found, cannot display response" ai-auto-complete-chat-prompt-prefix ai-auto-complete--chat-input-marker buffer-read-only ai-auto-complete--chat-in-progress] 11 (#$ . 32715)])#@54 Clear the chat history and restart the conversation.
(defalias 'ai-auto-complete-chat-clear #[nil "\205  	\203 \304\305!\203 \306\307!\210\304\310!\205  \311\211\311\312 \207" [ai-auto-complete-chat-mode ai-auto-complete--session-modified ai-auto-complete--chat-history ai-auto-complete--current-session-id y-or-n-p "Current session has unsaved changes. Save before clearing? " call-interactively ai-auto-complete-chat-session-save "Clear the current chat? " nil ai-auto-complete-chat-initialize] 3 (#$ . 34126) nil])#@56 Cancel the current chat request if one is in progress.
(defalias 'ai-auto-complete-chat-cancel-request #[nil "\205 \301\302!\210\303\304!\207" [ai-auto-complete--chat-in-progress message "Cancelling current chat request" ai-auto-complete-chat-handle-response "ERROR: Request cancelled by user."] 2 (#$ . 34655) nil])#@138 Display a tool call in the chat buffer.
   TOOL-NAME is the name of the tool being called.
   ARGS is a plist of arguments for the tool.
(defalias 'ai-auto-complete-chat-display-tool-call #[(tool-name args) "\306\307	\nGW\203/ 	\n8	T\n8\310\f!\203' \311\312\313\f!\314\315O#P	\316\\*\202 )\317\311\320
#\321\")\207" [args-str arg-index args value key tool-name "" 0 keywordp format "\n  %s: %s" symbol-name 1 nil 2 ai-auto-complete-chat-insert-message "%s%s" tool] 6 (#$ . 34980)])#@143 Display a tool result in the chat buffer.
   TOOL-NAME is the name of the tool that was called.
   RESULT is the result returned by the tool.
(defalias 'ai-auto-complete-chat-display-tool-result #[(tool-name result) "\302\303\304	#\305\"\207" [tool-name result ai-auto-complete-chat-insert-message format "%s returned:\n%s" tool-result] 5 (#$ . 35478)])#@53 Handle RESPONSE from AGENT-NAME in the chat buffer.
(defalias 'ai-auto-complete-chat-handle-agent-response #[(agent-name response) "\306!\203\307\310	\n\206 \311#\210r\306!q\210\312db\210\313 d|\210\n\204F \314\315\316\317\320\312\321\312\322\312&	c\210\314\323\324	\"\316\325\320\312\321\312\322\312&	c\210\202\357\n;\203x \326\n\327\330\312$\266\203\203x \314\315\316\317\320\312\321\312\322\312&	c\210\314\n\316\325\320\312\321\312\322\312&	c\210\202\357\f\203\276\331\n\327\330\312$\266\203\203\276\332\n!\307\333\n\"\210
\203\235
@
A78\314\323\334	\"\316\335\320\312\321\312\322\312&	c\210\314\n\320\312\321\312\322\312&c\210\314\336\320\312\321\312\322\312&c\210\314\323\3378\"\316\335\320\312\321\312\322\312&	c\210\314\340\316\341\320\312\321\312\322\312&	c\210\3129\307\342	87$\210\343:;<<\203\216<@=:T:\307\344:\345=!#\210=\203\205=<\203\205\346=!\204\205\3471x=@=A>?\307\350:?\211\351\267\202l\323\352>@\323\353>A\"\343\354\323\353>A\"G^O#\202q\323\353>\"\343\354\323\353>\"G^O\202q\323\353>\"$*0\210\202\205@\307\355:\356@!#)\210<A<)\202\373 *\35787;\360$*\210\202\272\314\323\334	\"\316\335\320\312\321\312\322\312&	c\210\314\n\320\312\321\312\322\312&c\210)\202\357\307\361	\n#\210A\203\322\362\n\363	#\210\202\357\314\323\334	\"\316\335\320\312\321\312\322\312&	c\210\314\n\320\312\321\312\322\312&c\210\314\336\320\312\321\312\322\312&c\210\314B\316\364\320\312\321\312\322\312&	c\210\365 C\327\211D\2119*\207\307\366!\207" [ai-auto-complete-chat-buffer-name agent-name response inhibit-read-only ai-auto-complete-agents-enabled agent-info get-buffer message "Received response from agent %s: %s" "nil" t line-beginning-position propertize "emacs: " face ai-auto-complete-system-face read-only front-sticky rear-nonsticky format "ERROR: Failed to get a response from agent %s. Please try again." error "^ERROR:" nil string-match "^@[a-zA-Z0-9_-]+\\s-+" ai-auto-complete-message-to-agent "DEBUG-CHAT: Response contains message to another agent: %s" "AGENT-%s: " ai-auto-complete-agent-face "\n\n" "@%s: " "Thinking..." italic "DEBUG-CHAT: Processing message by agent %s for target agent %s: %s : message : %s" 0 "DEBUG-CHAT: History item %d - Type: %s" type-of functionp (error) "DEBUG-CHAT: History item %d - Role: %s, Content: %s" #s(hash-table size 2 test eq rehash-size 1.5 rehash-threshold 0.8125 purecopy t data (agent 320 user 346)) "Agent %s: %s" "%s" 50 "DEBUG-CHAT: Error processing history item %d: %s" error-message-string ai-auto-complete-process-agent-message #[(target-agent-name response) "\304\305\"\210\306	BB\nB\307\303!\203' \203' \310\311!\203' \304\312!\210\311	\313#\207\314	\"\207" [target-agent-name response ai-auto-complete--chat-history ai-auto-complete-tools-enabled message "DEBUG-CHAT: Adding response from target agent %s to history" agent boundp fboundp ai-auto-complete-tools-process-response "DEBUG-CHAT: Processing response for tools" #[(processed-response) "\302	\"\207" [target-agent-name processed-response ai-auto-complete-chat-handle-agent-response] 3] ai-auto-complete-chat-handle-agent-response] 4] "DEBUG-CHAT: Processing regular response (not directed to any specific agent) from agent %s: %s" ai-auto-complete-streaming-simulate agent ai-auto-complete-user-face point-marker "Chat buffer not found, cannot display agent response" agent-message target-agent-name ai-auto-complete--chat-in-progress count ai-auto-complete--chat-history #1=#:tail msg content role err ai-auto-complete-enhanced-ui-enabled ai-auto-complete-chat-prompt-prefix ai-auto-complete--chat-input-marker buffer-read-only] 14 (#$ . 35838)])#@60 Toggle display of tool results in the current chat buffer.
(defalias 'ai-auto-complete-chat-toggle-tool-results #[nil "?\301\302\203
 \303\202 \304\"\210\305 \207" [ai-auto-complete--chat-show-tool-results message "Tool results %s" "enabled" "disabled" ai-auto-complete-chat-refresh] 3 (#$ . 39501) nil])#@63 Toggle display of control buttons in the current chat buffer.
(defalias 'ai-auto-complete-chat-toggle-control-buttons #[nil "?\301\302\203
 \303\202 \304\"\210\305 \207" [ai-auto-complete-chat-show-control-buttons message "Control buttons %s" "enabled" "disabled" ai-auto-complete-chat-refresh] 3 (#$ . 39815) nil])#@62 Toggle the sidebar for agent management and context display.
(defalias 'ai-auto-complete-chat-toggle-sidebar #[nil "\301\300!\203 \203 \302!\203 \303\304!\203 \304 \210\305\306!\207\303\307!\205) \307 \210\305\310!\207" [ai-auto-complete-sidebar-window boundp window-live-p fboundp ai-auto-complete-sidebar-close message "Sidebar closed" ai-auto-complete-sidebar-show "Sidebar opened"] 2 (#$ . 40140) nil])
(byte-code "\300\301\302\303\304\305\306\307&\210\310\311\312\313\314\315\304\301&\210\310\316\312\317\314\315\304\301&\210\310\320\312\321\314\315\304\301&\210\310\322\323\324\314\325\304\301&\207" [custom-declare-group ai-auto-complete-chat-features nil "Feature settings for AI Auto Complete chat." :group ai-auto-complete-chat :prefix "ai-auto-complete-chat-" custom-declare-variable ai-auto-complete-chat-default-streaming t "Whether to enable streaming by default in new chat buffers." :type boolean ai-auto-complete-chat-default-markdown "Whether to enable markdown rendering by default in new chat buffers." ai-auto-complete-chat-default-timestamps "Whether to show timestamps by default in new chat buffers." ai-auto-complete-chat-default-profile 'enhanced "Default profile for new chat buffers: 'enhanced, 'minimal, or 'custom." (choice (const :tag "Enhanced" enhanced) (const :tag "Minimal" minimal) (const :tag "Custom" custom))] 8)#@52 Set chat to minimal profile (basic features only).
(defalias 'ai-auto-complete-chat-set-minimal-profile #[nil "\303\211\303\304 \207" [ai-auto-complete-chat-streaming-enabled ai-auto-complete-chat-markdown-enabled ai-auto-complete-chat-show-timestamps nil ai-auto-complete-chat-refresh] 2 (#$ . 41508) nil])#@54 Set chat to enhanced profile (all features enabled).
(defalias 'ai-auto-complete-chat-set-enhanced-profile #[nil "\303\211\303\304 \207" [ai-auto-complete-chat-streaming-enabled ai-auto-complete-chat-markdown-enabled ai-auto-complete-chat-show-timestamps t ai-auto-complete-chat-refresh] 2 (#$ . 41824) nil])
(byte-code "\306\211\306\211\306\211\306
\307\310\311\306\312\306\313\314!\207" [ai-auto-complete-chat-default-streaming ai-auto-complete-chat-default-markdown ai-auto-complete-chat-default-timestamps ai-auto-complete-chat-default-tool-calls ai-auto-complete-chat-default-tool-results ai-auto-complete-chat-default-agent-controls t enhanced minimal custom nil provide chat ai-auto-complete-chat-default-control-buttons ai-auto-complete-chat-default-profile] 3)
