;;; integration.el --- Integration of enhanced UI components -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides integration of the enhanced UI components with the existing chat system.
;; It hooks into the existing chat functions to add streaming, markdown rendering, and interactive elements.

;;; Code:

(require 'cl-lib)
(require 'ui/markdown-renderer)
(require 'ui/streaming)
(require 'ui/enhanced-chat)

;; Define variables for tracking integration state
(defvar ai-auto-complete-ui-integration-enabled nil
  "Whether the enhanced UI integration is enabled.")

;; Function to enable the enhanced UI integration
(defun ai-auto-complete-ui-integration-enable ()
  "Enable the enhanced UI integration."
  (interactive)
  (unless ai-auto-complete-ui-integration-enabled
    ;; Add advice to chat functions
    (advice-add 'ai-auto-complete-chat :around #'ai-auto-complete-ui-integration-chat-advice)
    (advice-add 'ai-auto-complete-chat-send-message :around #'ai-auto-complete-ui-integration-send-message-advice)
    (advice-add 'ai-auto-complete-chat-handle-response :around #'ai-auto-complete-ui-integration-handle-response-advice)
    (advice-add 'ai-auto-complete-chat-handle-agent-response :around #'ai-auto-complete-ui-integration-handle-response-advice)
    (advice-add 'ai-auto-complete-chat-insert-message :around #'ai-auto-complete-ui-integration-insert-message-advice)
    (advice-add 'ai-auto-complete-chat-display-conversation :around #'ai-auto-complete-ui-integration-display-conversation-advice)

    ;; Add advice to tool functions
    (advice-add 'ai-auto-complete-tools-process-response :around #'ai-auto-complete-ui-integration-tools-process-response-advice)
    (advice-add 'ai-auto-complete-chat-display-tool-call :around #'ai-auto-complete-ui-integration-display-tool-call-advice)

    ;; Set the integration as enabled
    (setq ai-auto-complete-ui-integration-enabled t)

     (ai-auto-complete-enhanced-chat-initialize)

    ;; Ensure proper input marker management
    (ai-auto-complete-ui-integration-ensure-input-marker)



    (message "Enhanced UI integration enabled")))

;; Function to disable the enhanced UI integration
(defun ai-auto-complete-ui-integration-disable ()
  "Disable the enhanced UI integration."
  (interactive)
  (when ai-auto-complete-ui-integration-enabled
    ;; Remove advice from chat functions
    (advice-remove 'ai-auto-complete-chat #'ai-auto-complete-ui-integration-chat-advice)
    (advice-remove 'ai-auto-complete-chat-send-message #'ai-auto-complete-ui-integration-send-message-advice)
    (advice-remove 'ai-auto-complete-chat-handle-response #'ai-auto-complete-ui-integration-handle-response-advice)
    (advice-remove 'ai-auto-complete-chat-handle-agent-response #'ai-auto-complete-ui-integration-handle-response-advice)
    (advice-remove 'ai-auto-complete-chat-insert-message #'ai-auto-complete-ui-integration-insert-message-advice)
    (advice-remove 'ai-auto-complete-chat-display-conversation #'ai-auto-complete-ui-integration-display-conversation-advice)

    ;; Remove advice from tool functions
    (advice-remove 'ai-auto-complete-tools-process-response #'ai-auto-complete-ui-integration-tools-process-response-advice)
    (advice-remove 'ai-auto-complete-chat-display-tool-call #'ai-auto-complete-ui-integration-display-tool-call-advice)

    ;; Disable enhanced chat mode in the chat buffer and restore regular chat mode
    (let ((chat-buffer (get-buffer ai-auto-complete-chat-buffer-name)))
      (when (buffer-live-p chat-buffer)
        (with-current-buffer chat-buffer
          ;; Disable enhanced chat mode if it's enabled
          (when ai-auto-complete-enhanced-chat-mode
            (ai-auto-complete-enhanced-chat-mode -1))
          ;; Clean up enhanced chat markers
          (when (fboundp 'ai-auto-complete-enhanced-chat-cleanup)
            (ai-auto-complete-enhanced-chat-cleanup))
          ;; Re-enable regular chat mode
          (unless (and (boundp 'ai-auto-complete-chat-mode) ai-auto-complete-chat-mode)
            (ai-auto-complete-chat-mode 1)))))

    ;; Set the integration as disabled
    (setq ai-auto-complete-ui-integration-enabled nil)

    ;; Ensure proper input marker management after disabling
    (ai-auto-complete-ui-integration-ensure-input-marker)

    (message "Enhanced UI integration disabled")))

;; Helper function to ensure proper input marker management
(defun ai-auto-complete-ui-integration-ensure-input-marker ()
  "Ensure the appropriate input marker is set based on the current mode."
  (with-current-buffer (get-buffer-create ai-auto-complete-chat-buffer-name)
    (if (and (boundp 'ai-auto-complete-enhanced-chat-mode)
             ai-auto-complete-enhanced-chat-mode)
        ;; Enhanced UI mode - ensure enhanced input marker is set
        (unless (and (boundp 'ai-auto-complete-enhanced-chat-input-marker)
                     ai-auto-complete-enhanced-chat-input-marker
                     (marker-position ai-auto-complete-enhanced-chat-input-marker))
          (goto-char (point-max))
          (setq ai-auto-complete-enhanced-chat-input-marker (point-marker)))
      ;; Regular chat mode - ensure regular input marker is set
      (unless (and (boundp 'ai-auto-complete--chat-input-marker)
                   ai-auto-complete--chat-input-marker
                   (marker-position ai-auto-complete--chat-input-marker))
        (goto-char (point-max))
        (setq ai-auto-complete--chat-input-marker (point-marker))))))

;; Advice functions for chat functions

(defun ai-auto-complete-ui-integration-chat-advice (orig-fun &rest args)
  "Advice for `ai-auto-complete-chat' to use the enhanced chat interface.
ORIG-FUN is the original function.
ARGS are the arguments to the original function."
  (if ai-auto-complete-ui-integration-enabled
      (progn
        ;; Use the enhanced chat interface directly
        (ai-auto-complete-enhanced-chat)
        ;; Ensure the enhanced chat minor mode is properly enabled
        (with-current-buffer (get-buffer-create ai-auto-complete-chat-buffer-name)
          ;; Disable regular chat mode if it's enabled
          (when (and (boundp 'ai-auto-complete-chat-mode) ai-auto-complete-chat-mode)
            (ai-auto-complete-chat-mode -1))
          ;; Enable enhanced chat mode if it's not already enabled
          (unless ai-auto-complete-enhanced-chat-mode
            (ai-auto-complete-enhanced-chat-mode 1)))
        ;; Ensure proper input marker management
        (ai-auto-complete-ui-integration-ensure-input-marker))
    (apply orig-fun args)))

(defun ai-auto-complete-ui-integration-send-message-advice (orig-fun &rest args)
  "Advice for `ai-auto-complete-chat-send-message' to use streaming.
ORIG-FUN is the original function.
ARGS are the arguments to the original function."
  (if ai-auto-complete-ui-integration-enabled
      (progn
        ;; Ensure the enhanced chat minor mode is properly enabled
        (with-current-buffer (get-buffer-create ai-auto-complete-chat-buffer-name)
          ;; Disable regular chat mode if it's enabled
          (when (and (boundp 'ai-auto-complete-chat-mode) ai-auto-complete-chat-mode)
            (ai-auto-complete-chat-mode -1))
          ;; Enable enhanced chat mode if it's not already enabled
          (unless ai-auto-complete-enhanced-chat-mode
            (ai-auto-complete-enhanced-chat-mode 1)))
        ;; Ensure proper input marker management
        (ai-auto-complete-ui-integration-ensure-input-marker)
        (ai-auto-complete-enhanced-chat-send-message))
    (apply orig-fun args)))

(defun ai-auto-complete-ui-integration-handle-response-advice (orig-fun response &rest args)
  "Advice for `ai-auto-complete-chat-handle-response' to use streaming.
ORIG-FUN is the original function.
RESPONSE is the response from the AI.
ARGS are the additional arguments to the original function."
  ;; This function is kept for backward compatibility
  ;; It now delegates to ai-auto-complete-chat-handle-agent-response
  (if ai-auto-complete-ui-integration-enabled
      (ai-auto-complete-streaming-simulate response)
    (apply orig-fun response args)))

(defun ai-auto-complete-ui-integration-insert-message-advice (orig-fun message role &rest args)
  "Advice for `ai-auto-complete-chat-insert-message' to use markdown rendering.
ORIG-FUN is the original function.
MESSAGE is the message to insert.
ROLE is the role of the message.
ARGS are the additional arguments to the original function."
  (if ai-auto-complete-ui-integration-enabled
      (cond
       ((eq role 'user)
        (ai-auto-complete-streaming-start 'user)
        (ai-auto-complete-streaming-update message)
        (ai-auto-complete-streaming-complete))
       ((eq role 'assistant)
        (ai-auto-complete-streaming-simulate message 'assistant))
       ((eq role 'agent)
        (let ((agent-name (car message))
              (content (cdr message)))
          (ai-auto-complete-streaming-simulate content 'agent agent-name)))
       ((eq role 'tool)
        (ai-auto-complete-streaming-start 'tool)
        (ai-auto-complete-streaming-update message)
        (ai-auto-complete-streaming-complete))
       ((eq role 'tool-result)
        (ai-auto-complete-streaming-start 'tool-result)
        (ai-auto-complete-streaming-update message)
        (ai-auto-complete-streaming-complete))
       (t (apply orig-fun message role args)))
    (apply orig-fun message role args)))

(defun ai-auto-complete-ui-integration-display-conversation-advice (orig-fun &rest args)
  "Advice for `ai-auto-complete-chat-display-conversation' to use the enhanced display.
ORIG-FUN is the original function.
ARGS are the arguments to the original function."
  (if ai-auto-complete-ui-integration-enabled
      (progn
        ;; Ensure the enhanced chat minor mode is properly enabled
        (with-current-buffer (get-buffer-create ai-auto-complete-chat-buffer-name)
          ;; Disable regular chat mode if it's enabled
          (when (and (boundp 'ai-auto-complete-chat-mode) ai-auto-complete-chat-mode)
            (ai-auto-complete-chat-mode -1))
          ;; Enable enhanced chat mode if it's not already enabled
          (unless ai-auto-complete-enhanced-chat-mode
            (ai-auto-complete-enhanced-chat-mode 1)))
        ;; Ensure proper input marker management
        (ai-auto-complete-ui-integration-ensure-input-marker)
        (ai-auto-complete-enhanced-chat-display-conversation))
    (apply orig-fun args)))

;; Advice functions for tool functions

(defun ai-auto-complete-ui-integration-tools-process-response-advice (orig-fun response callback &rest args)
  "Advice for `ai-auto-complete-tools-process-response' to use streaming for tool calls.
ORIG-FUN is the original function.
RESPONSE is the response to process.
CALLBACK is the callback function.
ARGS are the additional arguments to the original function."
  (if ai-auto-complete-ui-integration-enabled
      ;; Check if there are any tool calls in the response first
      (if (not (string-match-p "<tool name=" response))
          ;; No tool calls, just call the callback directly to avoid unnecessary processing
          (progn
            (message "UI integration: No tool calls in response, calling callback directly")
            (if (functionp callback)
                (funcall callback response)
              ;; If no callback, use the default callback
              (let ((agent-name (when args (car args))))
                (ai-auto-complete-tools-default-callback response agent-name))))

        ;; There are potential tool calls, parse them
        (let ((tool-calls (ai-auto-complete-tools-parse-response response)))
          (if (not tool-calls)
              ;; No valid tool calls found after parsing, just call the callback directly
              (progn
                (message "UI integration: No valid tool calls found after parsing, calling callback directly")
                (if (functionp callback)
                    (funcall callback response)
                  ;; If no callback, use the default callback
                  (let ((agent-name (when args (car args))))
                    (ai-auto-complete-tools-default-callback response agent-name))))

            ;; Process tool calls with streaming
            (progn
              (message "UI integration: Processing %d tool calls with streaming" (length tool-calls))

              ;; Only display the tool calls, don't execute them directly
              ;; Let the tools-state-machine handle the actual execution
              (dolist (tool-call tool-calls)
                (let ((tool-name (car tool-call))
                      (params (cdr tool-call)))
                  ;; Display the tool call
                  (ai-auto-complete-streaming-display-tool-call tool-name params)))

              ;; Delegate the actual tool execution to the tools-state-machine
              ;; Create a wrapped callback that will display tool results
              (let ((wrapped-callback (lambda (final-response)
                                        ;; Display any tool results that might be in the response
                                        (when (string-match-p "<tool_result name=" final-response)
                                          (let ((start 0))
                                            (while (string-match "<tool_result name=\"\\([^\"]+\\)\">" final-response start)
                                              (let* ((tool-name (match-string 1 final-response))
                                                     (result-start (match-end 0))
                                                     (result-end (string-match "</tool_result>" final-response result-start)))
                                                (when result-end
                                                  ;; Extract the result text
                                                  (let ((result-text (substring final-response result-start result-end)))
                                                    ;; Display the tool result
                                                    (ai-auto-complete-streaming-display-tool-result tool-name result-text)))
                                                ;; Move past this result
                                                (setq start (if result-end
                                                               (+ result-end (length "</tool_result>"))
                                                             ;; If no closing tag, move past the opening tag
                                                             (+ result-start 20)))))))

                                        ;; Call the original callback with the final response
                                        (when (functionp callback)
                                          (funcall callback final-response)))))

                ;; Let the tools-state-machine handle the actual execution
                (message "UI integration: Delegating tool execution to tools-state-machine")
                (ai-auto-complete-tools-process-with-state-machine response wrapped-callback (car args)))))))

    ;; UI integration not enabled, use the original function
    (apply orig-fun response callback args)))

(defun ai-auto-complete-ui-integration-display-tool-call-advice (orig-fun tool-name args &rest rest-args)
  "Advice for `ai-auto-complete-chat-display-tool-call' to use streaming.
ORIG-FUN is the original function.
TOOL-NAME is the name of the tool being called.
ARGS is a plist of arguments for the tool.
REST-ARGS are the additional arguments to the original function."
  (if ai-auto-complete-ui-integration-enabled
      (ai-auto-complete-streaming-display-tool-call tool-name args)
    (apply orig-fun tool-name args rest-args)))

(provide 'ui/integration)
;;; integration.el ends here
