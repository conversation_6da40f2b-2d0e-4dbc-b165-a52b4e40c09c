;;; test-unified-chat.el --- Test the unified chat mode -*- lexical-binding: t -*-

;;; Commentary:
;; Simple test to verify the unified chat mode works

;;; Code:

;; Load the necessary files
(load-file "chat-customization.el")
(load-file "chat-mode.el")
(load-file "chat.el")

;; Test function
(defun test-unified-chat ()
  "Test the unified chat mode."
  (interactive)
  (message "Testing unified chat mode...")
  
  ;; Test that the main function exists
  (if (fboundp 'ai-auto-complete-chat)
      (message "✓ ai-auto-complete-chat function exists")
    (error "✗ ai-auto-complete-chat function not found"))
  
  ;; Test that the mode exists
  (if (fboundp 'ai-auto-complete-chat-mode)
      (message "✓ ai-auto-complete-chat-mode function exists")
    (message "⚠ ai-auto-complete-chat-mode function not found (may be defined elsewhere)"))
  
  ;; Test that key variables exist
  (if (boundp 'ai-auto-complete-chat-streaming-enabled)
      (message "✓ Streaming variable exists")
    (error "✗ Streaming variable not found"))
  
  (if (boundp 'ai-auto-complete-chat-markdown-enabled)
      (message "✓ Markdown variable exists")
    (error "✗ Markdown variable not found"))
  
  (if (boundp 'ai-auto-complete-chat-show-timestamps)
      (message "✓ Timestamps variable exists")
    (error "✗ Timestamps variable not found"))
  
  ;; Test that toggle functions exist
  (if (fboundp 'ai-auto-complete-chat-toggle-streaming)
      (message "✓ Toggle streaming function exists")
    (error "✗ Toggle streaming function not found"))
  
  (if (fboundp 'ai-auto-complete-chat-toggle-markdown)
      (message "✓ Toggle markdown function exists")
    (error "✗ Toggle markdown function not found"))
  
  (if (fboundp 'ai-auto-complete-chat-toggle-timestamps)
      (message "✓ Toggle timestamps function exists")
    (error "✗ Toggle timestamps function not found"))
  
  ;; Test that profile functions exist
  (if (fboundp 'ai-auto-complete-chat-set-enhanced-profile)
      (message "✓ Enhanced profile function exists")
    (error "✗ Enhanced profile function not found"))
  
  (if (fboundp 'ai-auto-complete-chat-set-minimal-profile)
      (message "✓ Minimal profile function exists")
    (error "✗ Minimal profile function not found"))
  
  (message "✓ All basic tests passed! Unified chat mode is ready."))

;; Run the test
(test-unified-chat)

(provide 'test-unified-chat)
;;; test-unified-chat.el ends here
