{"type": "excalidraw", "version": 2, "source": "https://marketplace.visualstudio.com/items?itemName=pomdtr.excalidraw-editor", "elements": [{"id": "tUQ8OYL8sisYxLmpz6vvP", "type": "text", "x": 315.8828589033415, "y": 197.2153324562221, "width": 150.5291320289969, "height": 20, "angle": 0.044570500314094375, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "a1", "roundness": null, "seed": 150539713, "version": 363, "versionNonce": 1126490671, "isDeleted": false, "boundElements": [], "updated": 1747539455659, "link": null, "locked": false, "text": "...chat-send-input()", "fontSize": 16, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "...chat-send-input()", "autoResize": false, "lineHeight": 1.25}, {"id": "yZmF12DN_mHZ18G3ltABA", "type": "arrow", "x": 479.6378531277336, "y": 210.62493023669208, "width": 66.36214687226641, "height": 3.375069763307923, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "a2", "roundness": {"type": 2}, "seed": 2017158913, "version": 178, "versionNonce": 1934603489, "isDeleted": false, "boundElements": [], "updated": 1747539466875, "link": null, "locked": false, "points": [[0, 0], [14.362146872266408, 3.375069763307923], [66.36214687226641, 3.375069763307923]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "lJvjos1lqa5QDP5POFih7", "type": "text", "x": 571, "y": 206.5, "width": 247.6004638671875, "height": 20, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "a4", "roundness": null, "seed": 1242744897, "version": 126, "versionNonce": 1189262127, "isDeleted": false, "boundElements": [], "updated": 1747539485275, "link": null, "locked": false, "text": "...chat-process-agent-message()", "fontSize": 16, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "...chat-process-agent-message()", "autoResize": true, "lineHeight": 1.25}, {"id": "1NHQm9-Qg2j1U6_UndIoV", "type": "text", "x": 1088, "y": 395, "width": 301.919677734375, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "a6", "roundness": null, "seed": 1936543329, "version": 93, "versionNonce": 1872221361, "isDeleted": false, "boundElements": [{"id": "bQaHZ2e-Xvru8F1Nmzcv-", "type": "arrow"}, {"id": "j-ZRd_p5slYjsrHf-V-N-", "type": "arrow"}], "updated": 1748338715632, "link": null, "locked": false, "text": "...chat-handle-agent-response()", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "...chat-handle-agent-response()", "autoResize": true, "lineHeight": 1.25}, {"id": "gorIDVDtRTN260bpmAm4r", "type": "arrow", "x": 1154, "y": 433, "width": 158, "height": 33, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "a7", "roundness": {"type": 2}, "seed": 341089743, "version": 29, "versionNonce": 1406448687, "isDeleted": false, "boundElements": [], "updated": 1747538397451, "link": null, "locked": false, "points": [[0, 0], [-158, 33]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "CytHt0ztd2sGDRGzboKZQ", "type": "text", "x": 373, "y": 164, "width": 64.71994018554688, "height": 25, "angle": 0, "strokeColor": "#e03131", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aA", "roundness": null, "seed": 1795803663, "version": 22, "versionNonce": 1832537953, "isDeleted": false, "boundElements": [], "updated": 1747537809064, "link": null, "locked": false, "text": "chat.el", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "chat.el", "autoResize": true, "lineHeight": 1.25}, {"id": "q-SxngUBQpvWbV8cxnPos", "type": "text", "x": 555, "y": 167.5, "width": 108.3201904296875, "height": 20, "angle": 0, "strokeColor": "#e03131", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aB", "roundness": null, "seed": 1377345935, "version": 87, "versionNonce": 863570593, "isDeleted": false, "boundElements": [], "updated": 1747539471536, "link": null, "locked": false, "text": "agents-core.el", "fontSize": 16, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "agents-core.el", "autoResize": true, "lineHeight": 1.25}, {"id": "81IYK5F8lT6ZEbRvBgZvG", "type": "text", "x": 1244, "y": 355, "width": 64.71994018554688, "height": 25, "angle": 0, "strokeColor": "#e03131", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aD", "roundness": null, "seed": 805820609, "version": 11, "versionNonce": 780328687, "isDeleted": false, "boundElements": [], "updated": 1747538012805, "link": null, "locked": false, "text": "chat.el", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "chat.el", "autoResize": true, "lineHeight": 1.25}, {"id": "4LXS_zLxzT9Xe5D1bs5E2", "type": "arrow", "x": 846.6355939598028, "y": 224.5, "width": 56.364406040197196, "height": 1.5, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aE", "roundness": {"type": 2}, "seed": 1379741487, "version": 187, "versionNonce": 1468356865, "isDeleted": false, "boundElements": [], "updated": 1747539507540, "link": null, "locked": false, "points": [[0, 0], [56.364406040197196, 1.5]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "i8_p8csYkDkoAjGtCQQ31", "type": "text", "x": 920, "y": 216, "width": 269.69970703125, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aF", "roundness": null, "seed": 1978843311, "version": 173, "versionNonce": 277462031, "isDeleted": false, "boundElements": [{"id": "l2a5EZG-3_cwKDieh6yyr", "type": "arrow"}, {"id": "xq8SmRq4kBkcSZXfoIz25", "type": "arrow"}], "updated": 1747539510206, "link": null, "locked": false, "text": "ai-auto-complete-complete()", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "ai-auto-complete-complete()", "autoResize": true, "lineHeight": 1.25}, {"id": "l2a5EZG-3_cwKDieh6yyr", "type": "arrow", "x": 1075.1825236159568, "y": 252, "width": 192.81747638404318, "height": 82, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aG", "roundness": {"type": 2}, "seed": 626082991, "version": 545, "versionNonce": 1932878383, "isDeleted": false, "boundElements": [], "updated": 1747539510206, "link": null, "locked": false, "points": [[0, 0], [192.81747638404318, 82]], "lastCommittedPoint": null, "startBinding": {"elementId": "i8_p8csYkDkoAjGtCQQ31", "focus": 0.2126486220335267, "gap": 11}, "endBinding": null, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "X4hKJUjH16wy569j1cxC3", "type": "text", "x": 959, "y": 136, "width": 211.13031005859375, "height": 56.000000000000014, "angle": 0, "strokeColor": "#2f9e44", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aH", "roundness": null, "seed": 1992463919, "version": 452, "versionNonce": 232187727, "isDeleted": false, "boundElements": [], "updated": 1747539554832, "link": null, "locked": false, "text": "triggers tool definition \ninsertion  via advice function\nvia ", "fontSize": 14.933333333333337, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "triggers tool definition \ninsertion  via advice function\nvia ", "autoResize": true, "lineHeight": 1.25}, {"id": "gxGKI_DtnAfEFAwFIoP4m", "type": "text", "x": 1270, "y": 207, "width": 84.29991149902344, "height": 25, "angle": 0, "strokeColor": "#2f9e44", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aI", "roundness": null, "seed": 1464073761, "version": 11, "versionNonce": 425816660, "isDeleted": false, "boundElements": [{"id": "aVqtl46JcLu0DJgbvBn7J", "type": "arrow"}], "updated": 1748267360169, "link": null, "locked": false, "text": "response", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "response", "autoResize": true, "lineHeight": 1.25}, {"id": "niv-alc3Fiof8N8PoYa1n", "type": "text", "x": 1041, "y": 471, "width": 504.69952392578125, "height": 100, "angle": 0, "strokeColor": "#2f9e44", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aJ", "roundness": null, "seed": 1906719265, "version": 440, "versionNonce": 698975759, "isDeleted": false, "boundElements": [], "updated": 1747538501864, "link": null, "locked": false, "text": "triggers parsing for tools and\nif tools are found, ai-auto-complete-complete()\nmust be called again, along with agent-name\n", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "triggers parsing for tools and\nif tools are found, ai-auto-complete-complete()\nmust be called again, along with agent-name\n", "autoResize": false, "lineHeight": 1.25}, {"id": "BNkAZn3w9b3KFgJ8OmhaF", "type": "text", "x": 491, "y": 552, "width": 406.79949951171875, "height": 50, "angle": 0, "strokeColor": "#2f9e44", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aK", "roundness": null, "seed": 46306817, "version": 92, "versionNonce": 795633039, "isDeleted": false, "boundElements": [{"id": "cZ0h4Rbe0axh8Twcw5nlZ", "type": "arrow"}], "updated": 1747538760994, "link": null, "locked": false, "text": "ai-auto-complete-tools-start-processing()\n", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "ai-auto-complete-tools-start-processing()\n", "autoResize": true, "lineHeight": 1.25}, {"id": "YGKiHqg45d3QjdCfFpbbQ", "type": "text", "x": 492, "y": 483, "width": 512.119384765625, "height": 25, "angle": 0, "strokeColor": "#2f9e44", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aL", "roundness": null, "seed": 2065095713, "version": 133, "versionNonce": 1160392015, "isDeleted": false, "boundElements": [{"id": "cZ0h4Rbe0axh8Twcw5nlZ", "type": "arrow"}], "updated": 1747538760994, "link": null, "locked": false, "text": "ai-auto-complete-tools-process-with-state-machine()", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "ai-auto-complete-tools-process-with-state-machine()", "autoResize": true, "lineHeight": 1.25}, {"id": "cZ0h4Rbe0axh8Twcw5nlZ", "type": "arrow", "x": 662, "y": 522, "width": 1, "height": 25, "angle": 0, "strokeColor": "#2f9e44", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aN", "roundness": {"type": 2}, "seed": 1753498657, "version": 14, "versionNonce": 892709743, "isDeleted": false, "boundElements": [], "updated": 1747538760994, "link": null, "locked": false, "points": [[0, 0], [1, 25]], "lastCommittedPoint": null, "startBinding": {"elementId": "YGKiHqg45d3QjdCfFpbbQ", "focus": 0.33956889943889235, "gap": 14}, "endBinding": {"elementId": "BNkAZn3w9b3KFgJ8OmhaF", "focus": -0.1477484673632463, "gap": 5}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "xq8SmRq4kBkcSZXfoIz25", "type": "arrow", "x": 1084.0710937439924, "y": 212, "width": 13.825069146902933, "height": 112.99999999999997, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aO", "roundness": {"type": 2}, "seed": 1192771201, "version": 565, "versionNonce": 2084250991, "isDeleted": false, "boundElements": [], "updated": 1747539554833, "link": null, "locked": false, "points": [[0, 0], [13.825069146902933, -112.99999999999997]], "lastCommittedPoint": null, "startBinding": {"elementId": "i8_p8csYkDkoAjGtCQQ31", "focus": 0.19946253067465974, "gap": 4}, "endBinding": null, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "HjzPJ9mGBndc-DA0olIyX", "type": "text", "x": 1106, "y": 91, "width": 308.99261474609375, "height": 20, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aQ", "roundness": null, "seed": 937307233, "version": 41, "versionNonce": 1616522031, "isDeleted": false, "boundElements": [], "updated": 1747539586418, "link": null, "locked": false, "text": "ai-auto-complete-tools-advice-request()", "fontSize": 16, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "ai-auto-complete-tools-advice-request()", "autoResize": true, "lineHeight": 1.25}, {"id": "NPr8-bVsvoljg1KaEo8r1", "type": "text", "x": 1163, "y": 62, "width": 148.06430053710938, "height": 20, "angle": 0, "strokeColor": "#e03131", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aR", "roundness": null, "seed": 1218676481, "version": 30, "versionNonce": 384741167, "isDeleted": false, "boundElements": [], "updated": 1747539605738, "link": null, "locked": false, "text": "tools-integration.el", "fontSize": 16, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "tools-integration.el", "autoResize": true, "lineHeight": 1.25}, {"id": "X5BqNWw5YYod2ny2r4mpr", "type": "text", "x": 520, "y": 461, "width": 172.1283416748047, "height": 20, "angle": 0, "strokeColor": "#e03131", "backgroundColor": "#ffffff", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aS", "roundness": null, "seed": 698478447, "version": 24, "versionNonce": 445905953, "isDeleted": false, "boundElements": [], "updated": 1747539648788, "link": null, "locked": false, "text": "tools-state-machine.el", "fontSize": 16, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "tools-state-machine.el", "autoResize": true, "lineHeight": 1.25}, {"id": "aVqtl46JcLu0DJgbvBn7J", "type": "arrow", "x": 1364.462928179356, "y": 223.42857142857142, "width": 83.57142857142867, "height": 0, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aT", "roundness": {"type": 2}, "seed": 99727828, "version": 66, "versionNonce": 1677664852, "isDeleted": false, "boundElements": [], "updated": 1748267369281, "link": null, "locked": false, "points": [[0, 0], [83.57142857142867, 0]], "lastCommittedPoint": null, "startBinding": {"elementId": "gxGKI_DtnAfEFAwFIoP4m", "focus": 0.31428571428571256, "gap": 10.163016680332476}, "endBinding": null, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "aOwCu43AWb8rUwg-Rp8wt", "type": "text", "x": 1232.3473188741102, "y": 234.5, "width": 185.6597900390625, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aV", "roundness": null, "seed": 180700884, "version": 23, "versionNonce": 25882580, "isDeleted": false, "boundElements": [], "updated": 1748267421280, "link": null, "locked": false, "text": "ai...chat-send-input", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "ai...chat-send-input", "autoResize": true, "lineHeight": 1.25}, {"id": "1CHFzw8OFsiI_Pw4T8B5P", "type": "text", "x": 1481.1772138936417, "y": 221.28571428571433, "width": 274.37969970703125, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aW", "roundness": null, "seed": 948439764, "version": 46, "versionNonce": 392399212, "isDeleted": false, "boundElements": [], "updated": 1748267472531, "link": null, "locked": false, "text": "ai...tools-process-response()", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "ai...tools-process-response()", "autoResize": true, "lineHeight": 1.25}, {"id": "inKKALRbb6Z3TQd53H_9a", "type": "arrow", "x": 1618.0343567507844, "y": 202.71428571428572, "width": 0.7142857142855519, "height": 49.28571428571428, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aX", "roundness": {"type": 2}, "seed": 263162068, "version": 35, "versionNonce": 180164844, "isDeleted": false, "boundElements": [], "updated": 1748267484494, "link": null, "locked": false, "points": [[0, 0], [-0.7142857142855519, -49.28571428571428]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "Msos74BU_236S7eGf1jdI", "type": "text", "x": 1637.3200710364988, "y": 184.14285714285717, "width": 138.4398651123047, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aY", "roundness": null, "seed": 1812142956, "version": 36, "versionNonce": 2077712852, "isDeleted": false, "boundElements": [], "updated": 1748267497339, "link": null, "locked": false, "text": ":around advice", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": ":around advice", "autoResize": true, "lineHeight": 1.25}, {"id": "nYL0AdZVnR9K-STytUJwf", "type": "text", "x": 1533.0343567507844, "y": 86.2857142857143, "width": 461.5794677734375, "height": 50, "angle": 0, "strokeColor": "#e03131", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aZ", "roundness": null, "seed": 1338632812, "version": 208, "versionNonce": 1785513836, "isDeleted": false, "boundElements": [], "updated": 1748267615374, "link": null, "locked": false, "text": "..wrapped-callback & process-tools of\ntools-state-machine (mcp-tools-process advice)", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "..wrapped-callback & process-tools of\ntools-state-machine (mcp-tools-process advice)", "autoResize": true, "lineHeight": 1.25}, {"id": "bQaHZ2e-Xvru8F1Nmzcv-", "type": "arrow", "x": 1392.3200710364988, "y": 364.14285714285717, "width": 25, "height": 31.428571428571445, "angle": 0, "strokeColor": "#e03131", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aa", "roundness": {"type": 2}, "seed": 1490873044, "version": 18, "versionNonce": 1479269460, "isDeleted": false, "boundElements": [], "updated": 1748267621723, "link": null, "locked": false, "points": [[0, 0], [-25, 31.428571428571445]], "lastCommittedPoint": null, "startBinding": null, "endBinding": {"elementId": "1NHQm9-Qg2j1U6_UndIoV", "focus": 0.7387779004976983, "gap": 1}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "CEDjdJBWhI00FdtjKDqpw", "type": "text", "x": 1415.8914996079275, "y": 339.8571428571429, "width": 411.5396423339844, "height": 25, "angle": 0, "strokeColor": "#e03131", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "ab", "roundness": null, "seed": 894629868, "version": 51, "versionNonce": 1097281492, "isDeleted": false, "boundElements": [], "updated": 1748267635913, "link": null, "locked": false, "text": "this never happens when tools are enabled", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "this never happens when tools are enabled", "autoResize": true, "lineHeight": 1.25}, {"id": "4T24QdSj0OKKcV330zb72", "type": "arrow", "x": 1662.8599700927734, "y": 62.5, "width": 66, "height": 30, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "ac", "roundness": {"type": 2}, "seed": 1393580207, "version": 24, "versionNonce": 1560521263, "isDeleted": false, "boundElements": [], "updated": 1748312476342, "link": null, "locked": false, "points": [[0, 0], [66, -30]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "lsxGuu9KVE702x7s9POUC", "type": "text", "x": 1746.8599700927734, "y": 18.5, "width": 299.2796936035156, "height": 50, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "ae", "roundness": null, "seed": 2025638817, "version": 73, "versionNonce": 749287727, "isDeleted": false, "boundElements": [{"id": "vO8-918ZvqqwgOFlK5XUT", "type": "arrow"}, {"id": "t1812xJFubzLhllg8lViZ", "type": "arrow"}], "updated": 1748312666132, "link": null, "locked": false, "text": "ai...toosl-default-callback() of \ntools-core.el", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "ai...toosl-default-callback() of \ntools-core.el", "autoResize": true, "lineHeight": 1.25}, {"id": "vO8-918ZvqqwgOFlK5XUT", "type": "arrow", "x": 2046.8599700927734, "y": 36.5, "width": 711, "height": 398, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "af", "roundness": {"type": 2}, "seed": 550360705, "version": 286, "versionNonce": 1431367329, "isDeleted": false, "boundElements": [], "updated": 1748312567037, "link": null, "locked": false, "points": [[0, 0], [71, 398], [-640, 387]], "lastCommittedPoint": null, "startBinding": {"elementId": "lsxGuu9KVE702x7s9POUC", "focus": -0.9838367644064353, "gap": 1}, "endBinding": null, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "t1812xJFubzLhllg8lViZ", "type": "arrow", "x": 2054.422364394467, "y": 19.02640586878411, "width": 127.68355285928374, "height": 52.52640586878411, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "ah", "roundness": {"type": 2}, "seed": 755792481, "version": 93, "versionNonce": 450606081, "isDeleted": false, "boundElements": [], "updated": 1748312728732, "link": null, "locked": false, "points": [[0, 0], [127.68355285928374, -52.52640586878411]], "lastCommittedPoint": null, "startBinding": {"elementId": "lsxGuu9KVE702x7s9POUC", "focus": 0.4730216616250704, "gap": 8.282700698177953}, "endBinding": {"elementId": "8hT4_xGIDtJVA0WFUoCU3", "focus": 0, "gap": 9}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "8hT4_xGIDtJVA0WFUoCU3", "type": "text", "x": 2139.8599700927734, "y": -92.5, "width": 249.67974853515625, "height": 50, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "ai", "roundness": null, "seed": 361178401, "version": 72, "versionNonce": 570056737, "isDeleted": false, "boundElements": [{"id": "t1812xJFubzLhllg8lViZ", "type": "arrow"}], "updated": 1748312728732, "link": null, "locked": false, "text": "ai..chat-handle-response()\nof chat.el", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "ai..chat-handle-response()\nof chat.el", "autoResize": true, "lineHeight": 1.25}, {"id": "bYLdbGLQ1oIvtrEfB1hEy", "type": "text", "x": 2091.8599700927734, "y": 99.5, "width": 59.73994445800781, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aj", "roundness": null, "seed": 360437665, "version": 8, "versionNonce": 652863169, "isDeleted": false, "boundElements": [], "updated": 1748312701895, "link": null, "locked": false, "text": "case 1", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "case 1", "autoResize": true, "lineHeight": 1.25}, {"id": "5A6cr7XVgkqrbFBhl9wtt", "type": "text", "x": 2049.9899978637695, "y": -30, "width": 65.19993591308594, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "ak", "roundness": null, "seed": 369664047, "version": 23, "versionNonce": 1514296993, "isDeleted": false, "boundElements": [], "updated": 1748312732115, "link": null, "locked": false, "text": "case 2", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "case 2", "autoResize": true, "lineHeight": 1.25}, {"id": "j-ZRd_p5slYjsrHf-V-N-", "type": "arrow", "x": 1073.8599700927734, "y": 402.59142392147737, "width": 81.56001281738293, "height": 9.693847586857999, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "al", "roundness": {"type": 2}, "seed": 518550943, "version": 36, "versionNonce": 906896287, "isDeleted": false, "boundElements": null, "updated": 1748338763218, "link": null, "locked": false, "points": [[0, 0], [-81.56001281738293, -9.693847586857999]], "lastCommittedPoint": null, "startBinding": {"elementId": "1NHQm9-Qg2j1U6_UndIoV", "focus": -0.4640555776708879, "gap": 14.140029907226562}, "endBinding": {"elementId": "FYJO8IPugSxFYjJCX_GE1", "focus": -0.36705217008287383, "gap": 11.440216064453125}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "FYJO8IPugSxFYjJCX_GE1", "type": "text", "x": 758.8599700927734, "y": 375.5, "width": 221.99977111816406, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "am", "roundness": null, "seed": 40048383, "version": 76, "versionNonce": 2017061759, "isDeleted": false, "boundElements": [{"id": "j-ZRd_p5slYjsrHf-V-N-", "type": "arrow"}], "updated": 1748338763217, "link": null, "locked": false, "text": "ai..streaming-simulate()", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "ai..streaming-simulate()", "autoResize": true, "lineHeight": 1.25}, {"id": "RX4LTPofJgLM1vPY3bxGX", "type": "text", "x": 329.85997009277344, "y": 74.5, "width": 632.7993774414062, "height": 25, "angle": 0, "strokeColor": "#2f9e44", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "an", "roundness": null, "seed": 1309979647, "version": 191, "versionNonce": 1718217887, "isDeleted": false, "boundElements": null, "updated": 1748345085921, "link": null, "locked": false, "text": "ai-auto-complete-unified.el requires ui.el requires enhanced-chat.el", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "ai-auto-complete-unified.el requires ui.el requires enhanced-chat.el", "autoResize": true, "lineHeight": 1.25}, {"id": "EsyZ_15960S18VpnsTptu", "type": "text", "x": 447.85997009277344, "y": 38.5, "width": 1409.4989013671875, "height": 25, "angle": 0, "strokeColor": "#2f9e44", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "ao", "roundness": null, "seed": 1024766097, "version": 194, "versionNonce": 965388735, "isDeleted": false, "boundElements": null, "updated": 1748345377317, "link": null, "locked": false, "text": "Enable enhanced-ui-toogle => ai..ui-integration-enable() of integration.el => loads enhanced chat and all that takes over from old chat.el, i think!", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Enable enhanced-ui-toogle => ai..ui-integration-enable() of integration.el => loads enhanced chat and all that takes over from old chat.el, i think!", "autoResize": true, "lineHeight": 1.25}], "appState": {"gridSize": 20, "gridStep": 5, "gridModeEnabled": false, "viewBackgroundColor": "#ffffff"}, "files": {}}