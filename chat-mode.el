;;; chat-mode.el --- Chat mode for ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file contains the minor mode definition for chat functionality.

;;; Code:

(require 'chat-customization)
;; (require 'utils) ; Commented out - utils not available



;; Define the mode line variable if it's not already defined
(defvar ai-auto-complete--active-mode-line " AI:Chat"
  "Mode line indicator for AI Auto Complete modes.")

;; Define the pending request variable if it's not already defined
(defvar ai-auto-complete--pending-request nil
  "Flag to track if a request is in progress.")

;; Define the chat backend variable if it's not already defined
;(defvar ai-auto-complete-chat-backend nil
 ; "Backend to use for chat mode.")

;; Define the global backend variable if it's not already defined
;; Pending task: Comment this and delete later
(defvar ai-auto-complete-backend 'gemini
  "Default backend to use for AI Auto Complete.")

;; Initialize the chat backend to the global backend if not set
;(unless ai-auto-complete-chat-backend
 ; (setq ai-auto-complete-chat-backend ai-auto-complete-backend))

;; Chat mode variables
(defvar ai-auto-complete--chat-history nil
  "History of messages in the chat buffer.")

(defvar ai-auto-complete--chat-in-progress nil
  "Flag to track if a chat request is in progress.")

(defvar ai-auto-complete--chat-input-marker nil
  "Marker for the current input position in the chat buffer.")

(defvar ai-auto-complete--current-session-id nil
  "ID of the current chat session.")

(defvar ai-auto-complete--session-modified nil
  "Flag to track if the current session has been modified.")

;; Enhanced chat feature variables
(defvar-local ai-auto-complete-chat-streaming-enabled t
  "Whether streaming is enabled in this chat buffer.")

(defvar-local ai-auto-complete-chat-markdown-enabled t
  "Whether markdown rendering is enabled in this chat buffer.")

(defvar-local ai-auto-complete-chat-show-timestamps t
  "Whether to show timestamps in this chat buffer.")

(defvar-local ai-auto-complete-chat-show-tool-calls t
  "Whether to show tool calls in this chat buffer.")

(defvar-local ai-auto-complete-chat-show-tool-results t
  "Whether to show tool results in this chat buffer.")

(defvar-local ai-auto-complete-chat-show-agent-controls t
  "Whether to show agent controls in this chat buffer.")

(defvar-local ai-auto-complete-chat-show-control-buttons t
  "Whether to show control buttons in this chat buffer.")

(defvar-local ai-auto-complete-chat-active-agent nil
  "The currently active agent in the chat interface.")

;; Enhanced chat markers for better buffer management
(defvar-local ai-auto-complete-chat-header-marker nil
  "Marker for the header section in the chat buffer.")

(defvar-local ai-auto-complete-chat-content-marker nil
  "Marker for the content section in the chat buffer.")

(defvar-local ai-auto-complete-chat-footer-marker nil
  "Marker for the footer section in the chat buffer.")


(defvar ai-auto-complete-chat-mode-map
  (let ((map (make-sparse-keymap)))
    ;; Core chat functionality
    (define-key map (kbd "RET") 'ai-auto-complete-chat-send-input) ; this function is in chat.el
    (define-key map (kbd "C-c C-c") 'ai-auto-complete-chat-cancel-request)
    (define-key map (kbd "C-c C-k") 'ai-auto-complete-chat-clear)
    (define-key map (kbd "C-c C-n") 'ai-auto-complete-next-backend)
    ;(define-key map (kbd "C-c C-m") 'ai-auto-complete-select-openrouter-model)

    ;; Session management
    (define-key map (kbd "C-c C-s") 'ai-auto-complete-chat-session-save)
    (define-key map (kbd "C-c C-l") 'ai-auto-complete-chat-session-load)

    ;; Context management
    (define-key map (kbd "C-c C-f") 'ai-auto-complete-add-file-to-context)
    (define-key map (kbd "C-c C-b") 'ai-auto-complete-add-buffer-to-context)

    ;; Enhanced UI shortcuts - now built into the unified mode
    (define-key map (kbd "C-c C-t") 'ai-auto-complete-chat-toggle-timestamps)
    (define-key map (kbd "C-c C-a") 'ai-auto-complete-chat-toggle-message-actions)
    (define-key map (kbd "C-c C-g") 'ai-auto-complete-chat-select-agent)
    (define-key map (kbd "C-c C-r") 'ai-auto-complete-chat-refresh)
    (define-key map (kbd "C-c C-m") 'ai-auto-complete-chat-toggle-markdown)
    (define-key map (kbd "C-c C-e") 'ai-auto-complete-chat-toggle-streaming)
    (define-key map (kbd "C-c C-u") 'ai-auto-complete-chat-toggle-tool-results)
    (define-key map (kbd "C-c C-i") 'ai-auto-complete-chat-toggle-control-buttons)
    (define-key map (kbd "C-c C-d") 'ai-auto-complete-chat-toggle-sidebar)

    ;; Navigation shortcuts
    (define-key map (kbd "C-c C-j") 'ai-auto-complete-chat-scroll-to-bottom)
    (define-key map (kbd "C-c C-h") 'ai-auto-complete-chat-scroll-to-top)
    (define-key map (kbd "C-c C-p") 'ai-auto-complete-chat-previous-message)
    (define-key map (kbd "C-c C-o") 'ai-auto-complete-chat-next-message)

    map)
  "Keymap for ai-auto-complete-chat-mode.")


;;;###autoload
(define-minor-mode ai-auto-complete-chat-mode
  "Minor mode for AI-assisted chat interface with enhanced features."
  :lighter ai-auto-complete--active-mode-line
  :keymap ai-auto-complete-chat-mode-map
  :group 'ai-auto-complete-chat
  (if ai-auto-complete-chat-mode
      (progn
        (ai-auto-complete--update-mode-line)
        ;; Initialize enhanced features with default values
        (ai-auto-complete-chat-initialize-features)
        ;; Set buffer as read-only but ensure input area is writable
        (setq buffer-read-only t))
    (when ai-auto-complete--chat-in-progress
      (ai-auto-complete-chat-cancel-request))))

;; Define missing function
(defun ai-auto-complete--update-mode-line ()
  "Update the mode line indicator."
  (setq ai-auto-complete--active-mode-line " AI:Chat"))

;; Feature initialization function
(defun ai-auto-complete-chat-initialize-features ()
  "Initialize enhanced chat features with default values."
  ;; Check if we should use a profile or individual settings
  (let ((profile (if (boundp 'ai-auto-complete-chat-default-profile)
                     ai-auto-complete-chat-default-profile
                   'enhanced)))
    (cond
     ;; Minimal profile - basic features only
     ((eq profile 'minimal)
      (setq ai-auto-complete-chat-streaming-enabled nil
            ai-auto-complete-chat-markdown-enabled nil
            ai-auto-complete-chat-show-timestamps nil
            ai-auto-complete-chat-show-tool-calls nil
            ai-auto-complete-chat-show-tool-results nil
            ai-auto-complete-chat-show-agent-controls nil
            ai-auto-complete-chat-show-control-buttons nil))

     ;; Enhanced profile - all features enabled
     ((eq profile 'enhanced)
      (setq ai-auto-complete-chat-streaming-enabled t
            ai-auto-complete-chat-markdown-enabled t
            ai-auto-complete-chat-show-timestamps t
            ai-auto-complete-chat-show-tool-calls t
            ai-auto-complete-chat-show-tool-results t
            ai-auto-complete-chat-show-agent-controls t
            ai-auto-complete-chat-show-control-buttons t))

     ;; Custom profile - use individual settings
     ((eq profile 'custom)
      (setq ai-auto-complete-chat-streaming-enabled
            (if (boundp 'ai-auto-complete-chat-default-streaming)
                ai-auto-complete-chat-default-streaming
              t))

      (setq ai-auto-complete-chat-markdown-enabled
            (if (boundp 'ai-auto-complete-chat-default-markdown)
                ai-auto-complete-chat-default-markdown
              t))

      (setq ai-auto-complete-chat-show-timestamps
            (if (boundp 'ai-auto-complete-chat-default-timestamps)
                ai-auto-complete-chat-default-timestamps
              t))

      (setq ai-auto-complete-chat-show-tool-calls
            (if (boundp 'ai-auto-complete-chat-default-tool-calls)
                ai-auto-complete-chat-default-tool-calls
              t))

      (setq ai-auto-complete-chat-show-tool-results
            (if (boundp 'ai-auto-complete-chat-default-tool-results)
                ai-auto-complete-chat-default-tool-results
              t))

      (setq ai-auto-complete-chat-show-agent-controls
            (if (boundp 'ai-auto-complete-chat-default-agent-controls)
                ai-auto-complete-chat-default-agent-controls
              t))

      (setq ai-auto-complete-chat-show-control-buttons
            (if (boundp 'ai-auto-complete-chat-default-control-buttons)
                ai-auto-complete-chat-default-control-buttons
              t)))

     ;; Default fallback to enhanced
     (t
      (setq ai-auto-complete-chat-streaming-enabled t
            ai-auto-complete-chat-markdown-enabled t
            ai-auto-complete-chat-show-timestamps t
            ai-auto-complete-chat-show-tool-calls t
            ai-auto-complete-chat-show-tool-results t
            ai-auto-complete-chat-show-agent-controls t
            ai-auto-complete-chat-show-control-buttons t)))))

(provide 'chat-mode)
;;; chat-mode.el ends here
