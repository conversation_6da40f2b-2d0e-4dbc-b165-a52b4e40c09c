;;; chat-customization.el --- Customization for chat interface -*- lexical-binding: t -*-

;;; Commentary:
;; This file contains customization options for the chat interface functionality.

;;; Code:

(defgroup ai-auto-complete-chat nil
  "AI-assisted chat interface."
  :group 'ai-auto-complete
  :prefix "ai-auto-complete-chat-")

(defcustom ai-auto-complete-chat-buffer-name "*AI Auto Complete Chat*"
  "Name of the chat buffer."
  :type 'string
  :group 'ai-auto-complete-chat)

(defcustom ai-auto-complete-chat-max-history 50
  "Maximum number of messages to keep in chat history."
  :type 'integer
  :group 'ai-auto-complete-chat)

(defcustom ai-auto-complete-chat-prompt-prefix "USER: "
  "Prefix for user messages in the chat interface."
  :type 'string
  :group 'ai-auto-complete-chat)

(defcustom ai-auto-complete-chat-response-prefix "ASSISTANT: "
  "Prefix for AI responses in the chat interface."
  :type 'string
  :group 'ai-auto-complete-chat)

;; Define faces for chat interface
(defface ai-auto-complete-user-face
  '((t :foreground "#729fcf" :weight bold))
  "Face for user messages in chat."
  :group 'ai-auto-complete-chat)

(defface ai-auto-complete-assistant-face
  '((t :foreground "#8ae234" :weight bold))
  "Face for assistant messages in chat."
  :group 'ai-auto-complete-chat)

(defface ai-auto-complete-system-face
  '((t :foreground "#eeeeec" :slant italic))
  "Face for system messages in chat."
  :group 'ai-auto-complete-chat)

(defface ai-auto-complete-timestamp-face
  '((t :foreground "#888a85" :slant italic))
  "Face for timestamps in chat."
  :group 'ai-auto-complete-chat)

(provide 'chat-customization)
;;; chat-customization.el ends here
