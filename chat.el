
;;; chat.el --- Enhanced Chat interface for ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file contains the enhanced chat interface implementation for ai-auto-complete.
;; It provides a modern, feature-rich conversational interface to interact with AI models.
;; Features include: streaming responses, markdown rendering, message actions,
;; agent management, tool integration, and session management.

;;; Code:

;; Core dependencies
(require 'cl-lib)

;; Conditional loading of dependencies
(condition-case nil (require 'shared-context) (error nil))
(condition-case nil (require 'context) (error nil))
(condition-case nil (require 'core/backend) (error nil))
(condition-case nil (require 'customization/prompts) (error nil))
(condition-case nil (require 'chat-customization) (error nil))
(condition-case nil (require 'chat-sessions) (error nil))
(condition-case nil (require 'tools/tools-core) (error nil))
(condition-case nil (require 'agents/agents-core) (error nil))
(condition-case nil (require 'chat-mode) (error nil))

;; Load enhanced UI components conditionally
(condition-case nil (require 'ui/markdown-renderer) (error nil))
(condition-case nil (require 'ui/streaming) (error nil))
(condition-case nil (require 'ui/message-actions) (error nil))
(condition-case nil (require 'ui/collapsible) (error nil))
(condition-case nil (require 'ui/themes) (error nil))

;; Enable enhanced UI by default
(defvar ai-auto-complete-enhanced-ui-enabled t
  "Whether the enhanced UI features are enabled by default.")

;; Define missing variables if not already defined
(unless (boundp 'ai-auto-complete-chat-buffer-name)
  (defvar ai-auto-complete-chat-buffer-name "*AI Auto Complete Chat*"
    "Name of the chat buffer."))

(unless (boundp 'ai-auto-complete-chat-prompt-prefix)
  (defvar ai-auto-complete-chat-prompt-prefix "USER: "
    "Prefix for user messages in the chat interface."))

(unless (boundp 'ai-auto-complete-backend)
  (defvar ai-auto-complete-backend 'gemini
    "Default backend to use for AI Auto Complete."))

(unless (boundp 'ai-auto-complete--chat-history)
  (defvar-local ai-auto-complete--chat-history nil
    "History of messages in the chat buffer."))

(unless (boundp 'ai-auto-complete--chat-in-progress)
  (defvar-local ai-auto-complete--chat-in-progress nil
    "Flag to track if a chat request is in progress."))

(unless (boundp 'ai-auto-complete--chat-input-marker)
  (defvar-local ai-auto-complete--chat-input-marker nil
    "Marker for the current input position in the chat buffer."))

(unless (boundp 'ai-auto-complete--current-session-id)
  (defvar-local ai-auto-complete--current-session-id nil
    "ID of the current chat session."))

(unless (boundp 'ai-auto-complete--session-modified)
  (defvar-local ai-auto-complete--session-modified nil
    "Flag to track if the current session has been modified."))

;; Enhanced UI variables
(defvar-local ai-auto-complete--chat-header-marker nil
  "Marker for the header section in the chat buffer.")

(defvar-local ai-auto-complete--chat-content-marker nil
  "Marker for the content section in the chat buffer.")

(defvar-local ai-auto-complete--chat-active-agent nil
  "The currently active agent in the chat interface.")

(defvar-local ai-auto-complete-chat-streaming-enabled t
  "Whether streaming is enabled in this chat buffer.")

(defvar-local ai-auto-complete-chat-markdown-enabled t
  "Whether markdown rendering is enabled in this chat buffer.")

(defvar-local ai-auto-complete-chat-show-timestamps t
  "Whether to show timestamps in this chat buffer.")

(defvar ai-auto-complete--chat-show-tool-calls t
  "Whether to show tool calls in the chat interface.")

(defvar ai-auto-complete--chat-show-tool-results t
  "Whether to show tool results in the chat interface.")

;; Enable message actions by default
(defvar ai-auto-complete-message-actions-enabled t
  "Whether message actions (edit, copy, delete, regenerate) are enabled.")

(defvar ai-auto-complete-chat-message-hook nil
  "Hook run after a user sends a message in the chat interface.
The hook function receives the message text as its argument.")

(defvar ai-auto-complete-chat-request-timeout 30
  "Timeout in seconds for chat API requests.")

(defvar ai-auto-complete--chat-timeout-timer nil
  "Timer for tracking API request timeouts.")

(defvar ai-auto-complete-chat-show-control-buttons t
  "Whether control buttons are shown in the chat header.")

;; Enhanced chat keymap
(defvar ai-auto-complete-enhanced-chat-mode-map
  (let ((map (make-sparse-keymap)))
    ;; Navigation shortcuts
    (define-key map (kbd "C-c C-t") 'ai-auto-complete-chat-toggle-timestamps)
    (define-key map (kbd "C-c C-a") 'ai-auto-complete-chat-toggle-message-actions)
    (define-key map (kbd "C-c C-s") 'ai-auto-complete-chat-select-agent)
    (define-key map (kbd "C-c C-c") 'ai-auto-complete-chat-clear)
    (define-key map (kbd "C-c C-l") 'ai-auto-complete-chat-load-session-action)
    (define-key map (kbd "C-c C-w") 'ai-auto-complete-chat-save-session-action)
    (define-key map (kbd "C-c C-r") 'ai-auto-complete-chat-refresh)
    ;; Scroll shortcuts
    (define-key map (kbd "C-c C-b") 'ai-auto-complete-chat-scroll-to-bottom)
    (define-key map (kbd "C-c C-h") 'ai-auto-complete-chat-scroll-to-top)
    ;; Message navigation
    (define-key map (kbd "C-c C-n") 'ai-auto-complete-chat-next-message)
    (define-key map (kbd "C-c C-p") 'ai-auto-complete-chat-previous-message)
    ;; Enhanced UI shortcuts
    (define-key map (kbd "C-c C-r") 'ai-auto-complete-chat-toggle-tool-results)
    (define-key map (kbd "C-c C-b") 'ai-auto-complete-chat-toggle-sidebar)
    (define-key map (kbd "C-c C-m") 'ai-auto-complete-chat-toggle-markdown)
    (define-key map (kbd "C-c C-s") 'ai-auto-complete-chat-toggle-streaming)
    map)
  "Keymap for enhanced chat mode.")

;; Enhanced UI faces
(defface ai-auto-complete-enhanced-chat-timestamp-face
  '((t :foreground "#888a85" :slant italic :height 0.8))
  "Face for timestamps in the enhanced chat interface."
  :group 'ai-auto-complete-chat)

(defface ai-auto-complete-enhanced-chat-tool-face
  '((t :foreground "#ad7fa8" :weight bold))
  "Face for tool calls in the enhanced chat interface."
  :group 'ai-auto-complete-chat)

(defface ai-auto-complete-enhanced-chat-tool-result-face
  '((t :foreground "#729fcf" :weight bold))
  "Face for tool results in the enhanced chat interface."
  :group 'ai-auto-complete-chat)

(defface ai-auto-complete-enhanced-chat-agent-face
  '((t :foreground "#fcaf3e" :weight bold))
  "Face for agent messages in the enhanced chat interface."
  :group 'ai-auto-complete-chat)

(defface ai-auto-complete-enhanced-chat-button-face
  '((t :box t :foreground "#729fcf" :background "#eeeeec"))
  "Face for buttons in the enhanced chat interface."
  :group 'ai-auto-complete-chat)

(defface ai-auto-complete-enhanced-chat-header-face
  '((t :height 1.2 :weight bold :foreground "#3465a4" :background "#eeeeec"))
  "Face for the header in the enhanced chat interface."
  :group 'ai-auto-complete-chat)

;; Message bubble faces
(defface ai-auto-complete-enhanced-chat-user-bubble-face
  '((t :background "#e3f2fd" :foreground "#1976d2" :box (:line-width 1 :color "#bbdefb" :style nil)))
  "Face for user message bubbles in the enhanced chat interface."
  :group 'ai-auto-complete-chat)

(defface ai-auto-complete-enhanced-chat-assistant-bubble-face
  '((t :background "#f3e5f5" :foreground "#7b1fa2" :box (:line-width 1 :color "#ce93d8" :style nil)))
  "Face for assistant message bubbles in the enhanced chat interface."
  :group 'ai-auto-complete-chat)

(defface ai-auto-complete-enhanced-chat-agent-bubble-face
  '((t :background "#fff3e0" :foreground "#f57c00" :box (:line-width 1 :color "#ffcc02" :style nil)))
  "Face for agent message bubbles in the enhanced chat interface."
  :group 'ai-auto-complete-chat)

(defface ai-auto-complete-enhanced-chat-tool-bubble-face
  '((t :background "#e8f5e8" :foreground "#388e3c" :box (:line-width 1 :color "#a5d6a7" :style nil)))
  "Face for tool message bubbles in the enhanced chat interface."
  :group 'ai-auto-complete-chat)

(defface ai-auto-complete-enhanced-chat-separator-face
  '((t :foreground "#bdbdbd" :height 0.8))
  "Face for message separators in the enhanced chat interface."
  :group 'ai-auto-complete-chat)

;; Define basic faces if not already defined
(unless (facep 'ai-auto-complete-user-face)
  (defface ai-auto-complete-user-face
    '((t :foreground "#729fcf" :weight bold))
    "Face for user messages in chat."
    :group 'ai-auto-complete-chat))

(unless (facep 'ai-auto-complete-assistant-face)
  (defface ai-auto-complete-assistant-face
    '((t :foreground "#8ae234" :weight bold))
    "Face for assistant messages in chat."
    :group 'ai-auto-complete-chat))

(unless (facep 'ai-auto-complete-system-face)
  (defface ai-auto-complete-system-face
    '((t :foreground "#eeeeec" :slant italic))
    "Face for system messages in chat."
    :group 'ai-auto-complete-chat))

(unless (facep 'ai-auto-complete-agent-face)
  (defface ai-auto-complete-agent-face
    '((t :foreground "#fcaf3e" :weight bold))
    "Face for agent messages in chat."
    :group 'ai-auto-complete-chat))

;; Define string-empty-p if it's not available (normally provided by 's package)
(unless (fboundp 'string-empty-p)
  (defun string-empty-p (string)
    "Return t if STRING is empty or nil."
    (or (null string) (string= string ""))))

;; when this hook is run, the function #'ai-auto-complete-chat-send-to-backend
;; is called with an input message.
(add-hook 'ai-auto-complete-chat-message-hook #'ai-auto-complete-chat-send-to-backend)

(defun ai-auto-complete-chat-initialize ()
  "Initialize the chat buffer with all enhanced features."
  (let ((inhibit-read-only t))
    ;; Clear buffer
    (erase-buffer)

    ;; Create enhanced header
    (insert (propertize "AI Auto Complete Chat\n"
                       'face 'ai-auto-complete-enhanced-chat-header-face
                       'read-only t
                       'front-sticky t
                       'rear-nonsticky t))
    (insert (propertize (format-time-string "Started on %Y-%m-%d %H:%M:%S\n")
                       'face 'ai-auto-complete-enhanced-chat-timestamp-face
                       'read-only t
                       'front-sticky t
                       'rear-nonsticky t))

    ;; Add enhanced control buttons if enabled
    (when ai-auto-complete-chat-show-control-buttons
      (ai-auto-complete-chat-insert-control-buttons))

    ;; Set the header marker
    (setq ai-auto-complete--chat-header-marker (point-marker))

    ;; Add a separator
    (insert (propertize "\n" 'read-only t 'front-sticky t 'rear-nonsticky t))

    ;; Set the content marker
    (setq ai-auto-complete--chat-content-marker (point-marker))

    ;; Display existing conversation history if any
    (when (and (boundp 'ai-auto-complete--chat-history)
               ai-auto-complete--chat-history)
      (ai-auto-complete-chat-display-conversation))

    ;; Add some instructions
    (insert (propertize "Type your message below and press Enter to send.\n\n"
                       'face 'ai-auto-complete-system-face
                       'read-only t
                       'front-sticky t
                       'rear-nonsticky t))

    ;; Add the input prompt
    (insert (propertize ai-auto-complete-chat-prompt-prefix
                       'face 'ai-auto-complete-user-face
                       'read-only t
                       'front-sticky t
                       'rear-nonsticky t))

    ;; Set the input marker
    (setq ai-auto-complete--chat-input-marker (point-marker))

    ;; Make sure the cursor is at the input position
    (goto-char (point-max))

    ;; Ensure the buffer is writable at the input position
    (put-text-property (point) (point) 'read-only nil)

    ;; Ensure buffer is writable
    (setq buffer-read-only nil)))

;; Helper functions for the new send input function
(defun ai-auto-complete-chat-can-send-p ()
  "Check if we can send a message."
  (and (not ai-auto-complete--chat-in-progress)
       ai-auto-complete--chat-input-marker
       (marker-position ai-auto-complete--chat-input-marker)
       (>= (point) ai-auto-complete--chat-input-marker)))

(defun ai-auto-complete-chat-get-input-text ()
  "Get the current input text."
  (when (and ai-auto-complete--chat-input-marker
             (marker-position ai-auto-complete--chat-input-marker))
    (buffer-substring-no-properties
     ai-auto-complete--chat-input-marker (point-max))))

(defun ai-auto-complete-chat-send-with-streaming (input-text)
  "Send INPUT-TEXT with streaming enabled."
  ;; For now, fall back to traditional sending
  (ai-auto-complete-chat-send-traditional input-text))

(defun ai-auto-complete-chat-send-traditional (input-text)
  "Send INPUT-TEXT using traditional method."
  ;; Simple implementation for now
  (when (and input-text (not (string-empty-p (string-trim input-text))))
    (message "Sending message: %s" input-text)
    ;; Add message to history
    (push (cons 'user input-text) ai-auto-complete--chat-history)
    ;; Clear input
    (delete-region ai-auto-complete--chat-input-marker (point-max))
    ;; Add user message to buffer
    (goto-char (point-max))
    (insert (propertize (format "\n%s%s\n\n"
                               ai-auto-complete-chat-prompt-prefix
                               input-text)
                       'face 'ai-auto-complete-user-face
                       'read-only t
                       'front-sticky t
                       'rear-nonsticky t))
    ;; Add AI response placeholder
    (insert (propertize "AI: Thinking...\n\n"
                       'face 'ai-auto-complete-assistant-face
                       'read-only t
                       'front-sticky t
                       'rear-nonsticky t))
    ;; Add new input prompt
    (insert (propertize ai-auto-complete-chat-prompt-prefix
                       'face 'ai-auto-complete-user-face
                       'read-only t
                       'front-sticky t
                       'rear-nonsticky t))
    (setq ai-auto-complete--chat-input-marker (point-marker))
    (goto-char (point-max))))

;; Placeholder for missing functions
(defun ai-auto-complete-chat-insert-timestamp ()
  "Insert a timestamp."
  (insert (propertize (format-time-string "[%H:%M:%S] ")
                     'face 'ai-auto-complete-enhanced-chat-timestamp-face
                     'read-only t
                     'front-sticky t
                     'rear-nonsticky t)))

(defun ai-auto-complete-chat-insert-role-prefix (role)
  "Insert role prefix for ROLE."
  (let ((prefix (cond
                ((eq role 'user) "USER: ")
                ((eq role 'assistant) "AI: ")
                ((eq role 'agent) "AGENT: ")
                ((eq role 'tool) "TOOL: ")
                ((eq role 'tool-result) "RESULT: ")
                (t "UNKNOWN: "))))
    (insert (propertize prefix
                       'face (cond
                             ((eq role 'user) 'ai-auto-complete-user-face)
                             ((eq role 'assistant) 'ai-auto-complete-assistant-face)
                             ((eq role 'agent) 'ai-auto-complete-enhanced-chat-agent-face)
                             ((eq role 'tool) 'ai-auto-complete-enhanced-chat-tool-face)
                             ((eq role 'tool-result) 'ai-auto-complete-enhanced-chat-tool-result-face)
                             (t 'default))
                       'read-only t
                       'front-sticky t
                       'rear-nonsticky t))))

(defun ai-auto-complete-chat-insert-markdown-content (content)
  "Insert CONTENT with markdown rendering."
  (if (fboundp 'ai-auto-complete-markdown-render)
      (insert (propertize (ai-auto-complete-markdown-render content)
                         'read-only t
                         'front-sticky t
                         'rear-nonsticky t))
    (ai-auto-complete-chat-insert-plain-content content)))

(defun ai-auto-complete-chat-insert-plain-content (content)
  "Insert CONTENT as plain text."
  (insert (propertize content
                     'read-only t
                     'front-sticky t
                     'rear-nonsticky t)))

(defun ai-auto-complete-chat-insert-separator ()
  "Insert a message separator."
  (insert (propertize "\n\n"
                     'read-only t
                     'front-sticky t
                     'rear-nonsticky t)))

;; Main send input function that will be called by the keymap
(defun ai-auto-complete-chat-send-input ()
  "Send input with streaming support."
  (interactive)
  (when (ai-auto-complete-chat-can-send-p)
    (let ((input-text (ai-auto-complete-chat-get-input-text)))
      ;; Start streaming if enabled
      (if ai-auto-complete-chat-streaming-enabled
          (ai-auto-complete-chat-send-with-streaming input-text)
        (ai-auto-complete-chat-send-traditional input-text)))))

(defun ai-auto-complete-chat-insert-message (message role &optional metadata)
  "Insert message with all enhanced features."
  (let ((inhibit-read-only t))
    ;; Add timestamp if enabled
    (when ai-auto-complete-chat-show-timestamps
      (ai-auto-complete-chat-insert-timestamp))

    ;; Add role prefix
    (ai-auto-complete-chat-insert-role-prefix role)

    ;; Insert content with markdown if enabled
    (if ai-auto-complete-chat-markdown-enabled
        (ai-auto-complete-chat-insert-markdown-content message)
      (ai-auto-complete-chat-insert-plain-content message))

    ;; Add message actions if enabled
    (when ai-auto-complete-message-actions-enabled
      (ai-auto-complete-message-actions-insert-buttons role message))

    ;; Add separator
    (ai-auto-complete-chat-insert-separator)))

(defun ai-auto-complete-chat-toggle-streaming ()
  "Toggle streaming in the current chat buffer."
  (interactive)
  (setq ai-auto-complete-chat-streaming-enabled
        (not ai-auto-complete-chat-streaming-enabled))
  (message "Streaming %s"
           (if ai-auto-complete-chat-streaming-enabled "enabled" "disabled")))

(defun ai-auto-complete-chat-toggle-markdown ()
  "Toggle markdown rendering in the current chat buffer."
  (interactive)
  (setq ai-auto-complete-chat-markdown-enabled
        (not ai-auto-complete-chat-markdown-enabled))
  (ai-auto-complete-chat-refresh)
  (message "Markdown rendering %s"
           (if ai-auto-complete-chat-markdown-enabled "enabled" "disabled")))

(defun ai-auto-complete-chat ()
  "Start or switch to the AI auto complete chat buffer."
  (interactive)
  (message "Starting chat function with backend: %s" (if (boundp 'ai-auto-complete-chat-backend) ai-auto-complete-chat-backend 'undefined))

  ;; Ensure tools advice is properly set up if tools are enabled
  (when (and (boundp 'ai-auto-complete-tools-enabled)
             ai-auto-complete-tools-enabled)
    (message "Tools are enabled for chat mode"))

  (message "Set chat backend to %s" ai-auto-complete-backend)
  (let ((chat-buffer (get-buffer-create ai-auto-complete-chat-buffer-name)))
    (with-current-buffer chat-buffer
      ;; Set up the buffer with text-mode as the base mode
      (unless (eq major-mode 'text-mode)
        (text-mode))

      ;; Enable our chat minor mode
      (unless ai-auto-complete-chat-mode
        (ai-auto-complete-chat-mode 1)
        (message "Chat mode after enabling: %s" (if ai-auto-complete-chat-mode "enabled" "disabled")))

      ;; Initialize a new session if needed
      (unless ai-auto-complete--current-session-id
        (when ai-auto-complete-session-ask-on-new
          (call-interactively 'ai-auto-complete-chat-session-new)
          ;; If user canceled the session name prompt, generate a default one
          (unless ai-auto-complete--current-session-id
            (setq ai-auto-complete--current-session-id
                  (ai-auto-complete-chat--generate-session-id nil)))))

      ;; Initialize the chat buffer
      (ai-auto-complete-chat-initialize)

      ;; Always ensure the buffer is in a writable state
      (setq buffer-read-only nil)

      ;; Make sure the input marker is set
      (unless (and ai-auto-complete--chat-input-marker
                   (marker-position ai-auto-complete--chat-input-marker))
        (goto-char (point-max))
        (setq ai-auto-complete--chat-input-marker (point-marker))))

    (switch-to-buffer chat-buffer)

    ;; Make sure the cursor is at the input position
    (when (and ai-auto-complete--chat-input-marker
               (marker-position ai-auto-complete--chat-input-marker))
      (goto-char ai-auto-complete--chat-input-marker))))

;; Enhanced UI control buttons
(defun ai-auto-complete-chat-insert-control-buttons ()
  "Insert enhanced control buttons in the chat header."
  (let ((inhibit-read-only t))
    ;; Create a button bar
    (insert (propertize "[ "
                       'read-only t
                       'front-sticky t
                       'rear-nonsticky t))

    ;; Add agent selection button
    (insert-text-button "Select Agent"
                       'face 'ai-auto-complete-enhanced-chat-button-face
                       'action 'ai-auto-complete-chat-select-agent-action
                       'follow-link t
                       'help-echo "Select an agent to chat with")

    (insert (propertize " | "
                       'read-only t
                       'front-sticky t
                       'rear-nonsticky t))

    ;; Add clear chat button
    (insert-text-button "Clear Chat"
                       'face 'ai-auto-complete-enhanced-chat-button-face
                       'action 'ai-auto-complete-chat-clear-action
                       'follow-link t
                       'help-echo "Clear the chat history")

    (insert (propertize " | "
                       'read-only t
                       'front-sticky t
                       'rear-nonsticky t))

    ;; Add save session button
    (insert-text-button "Save Session"
                       'face 'ai-auto-complete-enhanced-chat-button-face
                       'action 'ai-auto-complete-chat-save-session-action
                       'follow-link t
                       'help-echo "Save the current chat session")

    (insert (propertize " | "
                       'read-only t
                       'front-sticky t
                       'rear-nonsticky t))

    ;; Add load session button
    (insert-text-button "Load Session"
                       'face 'ai-auto-complete-enhanced-chat-button-face
                       'action 'ai-auto-complete-chat-load-session-action
                       'follow-link t
                       'help-echo "Load a saved chat session")

    (insert (propertize " | "
                       'read-only t
                       'front-sticky t
                       'rear-nonsticky t))

    ;; Add toggle timestamps button
    (insert-text-button (if ai-auto-complete-chat-show-timestamps
                           "Hide Timestamps"
                         "Show Timestamps")
                       'face 'ai-auto-complete-enhanced-chat-button-face
                       'action 'ai-auto-complete-chat-toggle-timestamps-action
                       'follow-link t
                       'help-echo "Toggle display of timestamps")

    (insert (propertize " | "
                       'read-only t
                       'front-sticky t
                       'rear-nonsticky t))

    ;; Add toggle message actions button
    (insert-text-button (if ai-auto-complete-message-actions-enabled
                           "Hide Actions"
                         "Show Actions")
                       'face 'ai-auto-complete-enhanced-chat-button-face
                       'action 'ai-auto-complete-chat-toggle-message-actions-action
                       'follow-link t
                       'help-echo "Toggle message action buttons")

    (insert (propertize " | "
                       'read-only t
                       'front-sticky t
                       'rear-nonsticky t))

    ;; Add toggle markdown button
    (insert-text-button (if ai-auto-complete-chat-markdown-enabled
                           "Hide Markdown"
                         "Show Markdown")
                       'face 'ai-auto-complete-enhanced-chat-button-face
                       'action 'ai-auto-complete-chat-toggle-markdown-action
                       'follow-link t
                       'help-echo "Toggle markdown rendering")

    (insert (propertize " | "
                       'read-only t
                       'front-sticky t
                       'rear-nonsticky t))

    ;; Add toggle streaming button
    (insert-text-button (if ai-auto-complete-chat-streaming-enabled
                           "Hide Streaming"
                         "Show Streaming")
                       'face 'ai-auto-complete-enhanced-chat-button-face
                       'action 'ai-auto-complete-chat-toggle-streaming-action
                       'follow-link t
                       'help-echo "Toggle streaming responses")

    (insert (propertize " ]\n"
                       'read-only t
                       'front-sticky t
                       'rear-nonsticky t))))

;; Button action functions
(defun ai-auto-complete-chat-select-agent-action (button)
  "Action for the select agent button."
  (ai-auto-complete-chat-select-agent))

(defun ai-auto-complete-chat-clear-action (button)
  "Action for the clear chat button."
  (ai-auto-complete-chat-clear))

(defun ai-auto-complete-chat-save-session-action (button)
  "Action for the save session button."
  (when (fboundp 'ai-auto-complete-chat-session-save)
    (call-interactively 'ai-auto-complete-chat-session-save)))

(defun ai-auto-complete-chat-load-session-action (button)
  "Action for the load session button."
  (when (fboundp 'ai-auto-complete-chat-session-load)
    (call-interactively 'ai-auto-complete-chat-session-load)))

(defun ai-auto-complete-chat-toggle-timestamps-action (button)
  "Action for the toggle timestamps button."
  (ai-auto-complete-chat-toggle-timestamps))

(defun ai-auto-complete-chat-toggle-message-actions-action (button)
  "Action for the toggle message actions button."
  (ai-auto-complete-chat-toggle-message-actions))

(defun ai-auto-complete-chat-toggle-markdown-action (button)
  "Action for the toggle markdown button."
  (ai-auto-complete-chat-toggle-markdown))

(defun ai-auto-complete-chat-toggle-streaming-action (button)
  "Action for the toggle streaming button."
  (ai-auto-complete-chat-toggle-streaming))

;; Enhanced UI functions
(defun ai-auto-complete-chat-select-agent ()
  "Select an agent to chat with."
  (interactive)
  (when (and (boundp 'ai-auto-complete-agents-enabled)
             ai-auto-complete-agents-enabled
             (boundp 'ai-auto-complete-agents)
             (hash-table-p ai-auto-complete-agents))
    (let* ((agents (hash-table-keys ai-auto-complete-agents))
           (agent-name (completing-read "Select agent: " agents nil t)))
      (when (not (string-empty-p agent-name))
        (setq ai-auto-complete--chat-active-agent agent-name)
        (message "Selected agent: %s" agent-name)))))

(defun ai-auto-complete-chat-toggle-timestamps ()
  "Toggle display of timestamps."
  (interactive)
  (setq ai-auto-complete-chat-show-timestamps
        (not ai-auto-complete-chat-show-timestamps))
  (message "Timestamps %s"
           (if ai-auto-complete-chat-show-timestamps "enabled" "disabled"))
  (ai-auto-complete-chat-refresh))

(defun ai-auto-complete-chat-toggle-message-actions ()
  "Toggle display of message action buttons."
  (interactive)
  (setq ai-auto-complete-message-actions-enabled
        (not ai-auto-complete-message-actions-enabled))
  (message "Message actions %s"
           (if ai-auto-complete-message-actions-enabled "enabled" "disabled"))
  (ai-auto-complete-chat-refresh))

(defun ai-auto-complete-chat-refresh ()
  "Refresh the chat interface with current settings."
  (when (get-buffer ai-auto-complete-chat-buffer-name)
    (with-current-buffer ai-auto-complete-chat-buffer-name
      (let ((inhibit-read-only t)
            (input-text (when (and ai-auto-complete--chat-input-marker
                                  (marker-position ai-auto-complete--chat-input-marker))
                         (buffer-substring-no-properties
                          ai-auto-complete--chat-input-marker
                          (point-max)))))
        ;; Reinitialize the interface
        (ai-auto-complete-chat-initialize)

        ;; Restore the input text
        (when input-text
          (goto-char ai-auto-complete--chat-input-marker)
          (insert input-text))))))

;; Enhanced conversation display function
(defun ai-auto-complete-chat-display-conversation ()
  "Display the current conversation history in the enhanced chat buffer."
  (when (and (boundp 'ai-auto-complete--chat-history)
             ai-auto-complete--chat-history)
    (let ((inhibit-read-only t))
      ;; Find where the content begins
      (goto-char ai-auto-complete--chat-content-marker)

      ;; Clear the content area
      (delete-region ai-auto-complete--chat-content-marker
                    (if (and ai-auto-complete--chat-input-marker
                            (marker-position ai-auto-complete--chat-input-marker))
                        ai-auto-complete--chat-input-marker
                      (point-max)))

      ;; Display each message in the history (in reverse order since history is newest-first)
      (let ((index 0))
        (dolist (msg (reverse ai-auto-complete--chat-history))
          (let ((role (car msg))
                (content (cdr msg)))
            ;; Use bubble rendering if markdown is enabled, otherwise use traditional rendering
            (if ai-auto-complete-chat-markdown-enabled
                (ai-auto-complete-chat-render-message-bubble role content index)
              ;; Traditional rendering (fallback)
              (progn
                (cond
                 ((eq role 'user)
                  (when ai-auto-complete-chat-show-timestamps
                    (insert (propertize (format-time-string "[%H:%M:%S] ")
                                       'face 'ai-auto-complete-enhanced-chat-timestamp-face
                                       'read-only t
                                       'front-sticky t
                                       'rear-nonsticky t)))
                  (insert (propertize "USER: "
                                     'face 'ai-auto-complete-user-face
                                     'read-only t
                                     'front-sticky t
                                     'rear-nonsticky t))
                  (insert (propertize content
                                     'read-only t
                                     'front-sticky t
                                     'rear-nonsticky t
                                     'ai-auto-complete-message-role role
                                     'ai-auto-complete-message-index index)))

                 ((eq role 'assistant)
                  (when ai-auto-complete-chat-show-timestamps
                    (insert (propertize (format-time-string "[%H:%M:%S] ")
                                       'face 'ai-auto-complete-enhanced-chat-timestamp-face
                                       'read-only t
                                       'front-sticky t
                                       'rear-nonsticky t)))
                  (insert (propertize "AI: "
                                     'face 'ai-auto-complete-assistant-face
                                     'read-only t
                                     'front-sticky t
                                     'rear-nonsticky t))
                  (insert (propertize content
                                     'read-only t
                                     'front-sticky t
                                     'rear-nonsticky t
                                     'ai-auto-complete-message-role role
                                     'ai-auto-complete-message-index index)))

                 ((eq role 'agent)
                  (when ai-auto-complete-chat-show-timestamps
                    (insert (propertize (format-time-string "[%H:%M:%S] ")
                                       'face 'ai-auto-complete-enhanced-chat-timestamp-face
                                       'read-only t
                                       'front-sticky t
                                       'rear-nonsticky t)))
                  (let ((agent-name (car content))
                        (agent-content (cdr content)))
                    (insert (propertize (format "AGENT-%s: " agent-name)
                                       'face 'ai-auto-complete-agent-face
                                       'read-only t
                                       'front-sticky t
                                       'rear-nonsticky t))
                    (insert (propertize agent-content
                                       'read-only t
                                       'front-sticky t
                                       'rear-nonsticky t
                                       'ai-auto-complete-message-role role
                                       'ai-auto-complete-message-index index))))

                 ((eq role 'tool)
                  (when (and ai-auto-complete--chat-show-tool-calls
                            ai-auto-complete-chat-show-timestamps)
                    (insert (propertize (format-time-string "[%H:%M:%S] ")
                                       'face 'ai-auto-complete-enhanced-chat-timestamp-face
                                       'read-only t
                                       'front-sticky t
                                       'rear-nonsticky t)))
                  (when ai-auto-complete--chat-show-tool-calls
                    (insert (propertize "TOOL: "
                                       'face 'font-lock-function-name-face
                                       'read-only t
                                       'front-sticky t
                                       'rear-nonsticky t))
                    (insert (propertize content
                                       'read-only t
                                       'front-sticky t
                                       'rear-nonsticky t
                                       'ai-auto-complete-message-role role
                                       'ai-auto-complete-message-index index))))

                 ((eq role 'tool-result)
                  (when (and ai-auto-complete--chat-show-tool-results
                            ai-auto-complete-chat-show-timestamps)
                    (insert (propertize (format-time-string "[%H:%M:%S] ")
                                       'face 'ai-auto-complete-enhanced-chat-timestamp-face
                                       'read-only t
                                       'front-sticky t
                                       'rear-nonsticky t)))
                  (when ai-auto-complete--chat-show-tool-results
                    (insert (propertize "RESULT: "
                                       'face 'font-lock-doc-face
                                       'read-only t
                                       'front-sticky t
                                       'rear-nonsticky t))
                    (insert (propertize content
                                       'read-only t
                                       'front-sticky t
                                       'rear-nonsticky t
                                       'ai-auto-complete-message-role role
                                       'ai-auto-complete-message-index index)))))

                ;; Add message actions if enabled (traditional rendering)
                (when (and (fboundp 'ai-auto-complete-message-actions-insert-buttons)
                          ai-auto-complete-message-actions-enabled
                          (memq role '(user assistant agent)))
                  (let ((msg-content (if (eq role 'agent) (cdr content) content)))
                    (ai-auto-complete-message-actions-insert-buttons role msg-content index)))

                ;; Add a newline after each message (traditional rendering)
                (insert (propertize "\n\n"
                                   'read-only t
                                   'front-sticky t
                                   'rear-nonsticky t))))

            ;; Increment the index
            (setq index (1+ index))))))))

;; Enhanced message rendering with bubbles
(defun ai-auto-complete-chat-render-message-bubble (role content index)
  "Render a message with ROLE and CONTENT at INDEX using bubble styling."
  (let ((inhibit-read-only t)
        (bubble-face (cond
                     ((eq role 'user) 'ai-auto-complete-enhanced-chat-user-bubble-face)
                     ((eq role 'assistant) 'ai-auto-complete-enhanced-chat-assistant-bubble-face)
                     ((eq role 'agent) 'ai-auto-complete-enhanced-chat-agent-bubble-face)
                     ((eq role 'tool) 'ai-auto-complete-enhanced-chat-tool-bubble-face)
                     ((eq role 'tool-result) 'ai-auto-complete-enhanced-chat-tool-bubble-face)
                     (t 'default)))
        (role-label (cond
                    ((eq role 'user) "YOU")
                    ((eq role 'assistant) "AI")
                    ((eq role 'agent) (format "AGENT-%s" (if (consp content) (car content) "UNKNOWN")))
                    ((eq role 'tool) "TOOL")
                    ((eq role 'tool-result) "RESULT")
                    (t "UNKNOWN")))
        (message-content (if (and (eq role 'agent) (consp content))
                            (cdr content)
                          content)))

    ;; Add timestamp if enabled
    (when ai-auto-complete-chat-show-timestamps
      (insert (propertize (format-time-string "[%H:%M:%S] ")
                         'face 'ai-auto-complete-enhanced-chat-timestamp-face
                         'read-only t
                         'front-sticky t
                         'rear-nonsticky t)))

    ;; Add role label with enhanced styling
    (insert (propertize (format "%s: " role-label)
                       'face (cond
                             ((eq role 'user) 'ai-auto-complete-user-face)
                             ((eq role 'assistant) 'ai-auto-complete-assistant-face)
                             ((eq role 'agent) 'ai-auto-complete-enhanced-chat-agent-face)
                             ((eq role 'tool) 'ai-auto-complete-enhanced-chat-tool-face)
                             ((eq role 'tool-result) 'ai-auto-complete-enhanced-chat-tool-result-face)
                             (t 'default))
                       'read-only t
                       'front-sticky t
                       'rear-nonsticky t))

    ;; Add message content with bubble styling and markdown rendering
    (let ((rendered-content (if ai-auto-complete-chat-markdown-enabled
                               (ai-auto-complete-markdown-render message-content)
                             message-content)))
      ;; Add padding and bubble styling
      (insert (propertize "\n  " 'read-only t 'front-sticky t 'rear-nonsticky t))

      ;; Check if content should be collapsible
      (if (and ai-auto-complete-chat-markdown-enabled
               (fboundp 'ai-auto-complete-collapsible-create)
               (ai-auto-complete-chat-should-collapse-content rendered-content role))
          ;; Create collapsible section for long content
          (let ((title (format "%s Content (%d lines)"
                              (upcase (symbol-name role))
                              (length (split-string rendered-content "\n"))))
                (collapsible-content (ai-auto-complete-collapsible-create
                                     title
                                     (propertize rendered-content
                                                'face bubble-face
                                                'read-only t
                                                'front-sticky t
                                                'rear-nonsticky t
                                                'ai-auto-complete-message-role role
                                                'ai-auto-complete-message-index index)
                                     (cond
                                      ((eq role 'tool-result) 'tool-result)
                                      ((string-match-p "```" rendered-content) 'code-block)
                                      (t 'content))
                                     'collapsed)))
            (insert (propertize collapsible-content
                               'read-only t
                               'front-sticky t
                               'rear-nonsticky t)))
        ;; Regular content display
        (insert (propertize rendered-content
                           'face bubble-face
                           'read-only t
                           'front-sticky t
                           'rear-nonsticky t
                           'ai-auto-complete-message-role role
                           'ai-auto-complete-message-index index)))

      (insert (propertize "\n  " 'read-only t 'front-sticky t 'rear-nonsticky t)))

    ;; Add message actions if enabled
    (when (and ai-auto-complete-message-actions-enabled
              (fboundp 'ai-auto-complete-message-actions-insert-buttons)
              (memq role '(user assistant agent)))
      (ai-auto-complete-message-actions-insert-buttons role message-content index))

    ;; Add separator
    (insert (propertize "\n" 'read-only t 'front-sticky t 'rear-nonsticky t))
    (insert (propertize "────────────────────────────────────────────────────────────────\n"
                       'face 'ai-auto-complete-enhanced-chat-separator-face
                       'read-only t
                       'front-sticky t
                       'rear-nonsticky t))
    (insert (propertize "\n" 'read-only t 'front-sticky t 'rear-nonsticky t))))

;; Function to determine if content should be collapsed
(defun ai-auto-complete-chat-should-collapse-content (content role)
  "Determine if CONTENT with ROLE should be collapsed."
  (let ((line-count (length (split-string content "\n")))
        (char-count (length content)))
    (or
     ;; Collapse if more than 15 lines
     (> line-count 15)
     ;; Collapse if more than 1000 characters
     (> char-count 1000)
     ;; Collapse tool results that are more than 5 lines
     (and (eq role 'tool-result) (> line-count 5))
     ;; Collapse code blocks that are more than 10 lines
     (and (string-match-p "```" content) (> line-count 10)))))

;; Smooth scroll to bottom function
(defun ai-auto-complete-chat-scroll-to-bottom ()
  "Smoothly scroll the chat buffer to the bottom."
  (when (get-buffer ai-auto-complete-chat-buffer-name)
    (with-current-buffer ai-auto-complete-chat-buffer-name
      (let ((windows (get-buffer-window-list (current-buffer))))
        (dolist (window windows)
          (with-selected-window window
            ;; Smooth scroll to bottom
            (goto-char (point-max))
            (recenter -1)
            ;; Ensure the input area is visible
            (when (and ai-auto-complete--chat-input-marker
                      (marker-position ai-auto-complete--chat-input-marker))
              (goto-char ai-auto-complete--chat-input-marker))))))))

;; Auto-scroll during streaming
(defun ai-auto-complete-chat-auto-scroll ()
  "Auto-scroll during streaming responses."
  (when (and ai-auto-complete-chat-streaming-enabled
             ai-auto-complete--chat-in-progress)
    (ai-auto-complete-chat-scroll-to-bottom)))

;; Navigation functions
(defun ai-auto-complete-chat-scroll-to-top ()
  "Scroll to the top of the chat buffer."
  (interactive)
  (when (get-buffer ai-auto-complete-chat-buffer-name)
    (with-current-buffer ai-auto-complete-chat-buffer-name
      (goto-char (point-min))
      (recenter 0))))

(defun ai-auto-complete-chat-next-message ()
  "Navigate to the next message in the chat."
  (interactive)
  (let ((next-pos (next-single-property-change (point) 'ai-auto-complete-message-role)))
    (when next-pos
      (goto-char next-pos)
      (recenter))))

(defun ai-auto-complete-chat-previous-message ()
  "Navigate to the previous message in the chat."
  (interactive)
  (let ((prev-pos (previous-single-property-change (point) 'ai-auto-complete-message-role)))
    (when prev-pos
      (goto-char prev-pos)
      (recenter))))

(defun ai-auto-complete-chat-timeout-handler ()
  "Handle timeout for chat API requests."
  (when ai-auto-complete--chat-in-progress
    (message "API request timed out after %d seconds" ai-auto-complete-chat-request-timeout)
    (ai-auto-complete-chat-handle-response "ERROR: Request timed out. The API did not respond within the expected time. Please try again or switch to a different backend.")
    (setq ai-auto-complete--chat-timeout-timer nil)))

(defun ai-auto-complete-chat-send-to-backend (input-text)
  "Send INPUT-TEXT to the current backend and handle the response."
  (message "Starting ai-auto-complete-chat-send-to-backend with input: %s" (substring input-text 0 (min 20 (length input-text))))

  ;; Ensure tools advice is applied if tools are enabled
  (when (and (boundp 'ai-auto-complete-tools-enabled)
             ai-auto-complete-tools-enabled
             (fboundp 'advice-member-p)
             (fboundp 'advice-add)
             (not (advice-member-p 'ai-auto-complete-tools-advice-request 'ai-auto-complete-complete)))
    (advice-add 'ai-auto-complete-complete :around #'ai-auto-complete-tools-advice-request)
    (message "Added tools advice to ai-auto-complete-complete"))
  (let ((prompt (ai-auto-complete-chat-build-prompt)))
    ;; Set up timeout timer
    (when ai-auto-complete--chat-timeout-timer
      (cancel-timer ai-auto-complete--chat-timeout-timer))
    (setq ai-auto-complete--chat-timeout-timer
          (run-with-timer ai-auto-complete-chat-request-timeout nil
                          #'ai-auto-complete-chat-timeout-handler))

    ;; Use the appropriate backend
    (let ((backend ai-auto-complete-backend))
      (message "DEBUG: Using backend for request: %s" backend)
      (cl-case backend
        (gemini
         (message "DEBUG: Calling Gemini provider")
         (ai-auto-complete--gemini-complete prompt #'ai-auto-complete-chat-handle-response-with-cleanup))
        (openai
         (message "DEBUG: Calling OpenAI provider")
         (ai-auto-complete--openai-complete prompt #'ai-auto-complete-chat-handle-response-with-cleanup))
        (anthropic
         (message "DEBUG: Calling Anthropic provider")
         (ai-auto-complete--anthropic-complete prompt #'ai-auto-complete-chat-handle-response-with-cleanup))
        (openrouter
         (message "DEBUG: Calling OpenRouter provider")
         (ai-auto-complete--openrouter-complete prompt #'ai-auto-complete-chat-handle-response-with-cleanup))))))

(defun ai-auto-complete-chat-handle-response-with-cleanup (response)
  "Handle RESPONSE from the AI and clean up timers."
  ;; Debug message
  (message "Response handler called with response: %s" (if response (substring response 0 (min 50 (length response))) "nil"))
  (message "Response type: %s" (type-of response))
  (message "Response is string: %s" (stringp response))
  (message "Response is error: %s" (and (stringp response) (string-match-p "^ERROR:" response)))

  ;; We don't need to modify tools advice here, just process the response

  ;; Cancel the timeout timer if it exists
  (when ai-auto-complete--chat-timeout-timer
    (cancel-timer ai-auto-complete--chat-timeout-timer)
    (setq ai-auto-complete--chat-timeout-timer nil))

  ;; Temporarily disable tools processing due to argument mismatch
  ;; Forward directly to the main handler
  (ai-auto-complete-chat-handle-response response))

(defun ai-auto-complete-chat-build-prompt ()
  "Build a prompt from the chat history and shared context."
  (message "Chat history length: %s" (if ai-auto-complete--chat-history
					 (format "%d messages" (length ai-auto-complete--chat-history))
                                       "empty"))
  (message "Chat mode active: %s" (if (and (boundp 'ai-auto-complete-chat-mode) ai-auto-complete-chat-mode) "yes" "no"))
  ;; Get the appropriate system prompt
  (let* (
         (backend ai-auto-complete-backend)
         ;; Temporarily disable tools system prompt
         (system-prompt (ai-auto-complete-get-system-prompt backend))
         ;; Log the prompt being used for debugging
         (dummy (message "Chat using system prompt: %s" system-prompt))
         (history-text "")
         (shared-context (if (fboundp 'ai-auto-complete-get-context-for-prompt)
                             (ai-auto-complete-get-context-for-prompt)
                           ""))
         (tool-definitions ""))

    ;; Build history text from oldest to newest messages (limited by ai-auto-complete-chat-max-history)
    (let ((history (seq-take (reverse ai-auto-complete--chat-history) ai-auto-complete-chat-max-history)))
      (setq history-text "\n\nConversation history:\n")
      (dolist (msg history)
        (let ((role (car msg))
              (content (cdr msg)))
          (setq history-text (concat history-text
                                     (cond
                                      ((eq role 'user) "User: ")
                                      ((eq role 'agent) (format "Agent %s: " (car content)))
                                      ((eq role 'tool-result) "Tool Results: ")
                                      (t "Assistant: "))
                                     (if (eq role 'agent) (cdr content) content)
                                     "\n\n")))))

    ;; Add tool definitions if tools are enabled
    (when (and (boundp 'ai-auto-complete-tools-enabled)
               ai-auto-complete-tools-enabled
               (fboundp 'ai-auto-complete-get-tool-definitions))
      (setq tool-definitions (concat "\n\nTool definitions:\n"
                                     (ai-auto-complete-get-tool-definitions))))

    ;; Debug output to see what's being sent
    (message "Chat prompt components:")
					; (message "  System prompt: %s" system-prompt)
    (message "  Shared context length: %d" (length shared-context))
    (message "  History text length: %d" (length history-text))
    (message "  Tool definitions length: %d" (length tool-definitions))
					;(message "Chat prompt: %s" (concat system-prompt shared-context history-text tool-definitions))

    ;; More detailed debug output for shared context
    (when (not (string-empty-p shared-context))
      (message "Chat mode: Shared context found (length: %d). Preview: %s"
               (length shared-context)
               (if (> (length shared-context) 100)
                   (concat (substring shared-context 0 100) "...")
                 shared-context)))

    ;; Debug output for tool definitions
    (when (not (string-empty-p tool-definitions))
      (message "Chat mode: Tool definitions added (length: %d)"
               (length tool-definitions)))

    ;; Return the full prompt
    (concat system-prompt shared-context history-text tool-definitions)))

(defun ai-auto-complete-chat-handle-response (response)
  "Handle RESPONSE from the AI in the chat buffer."
  (if (get-buffer ai-auto-complete-chat-buffer-name)
      (progn
        ;; Debug output
        (message "Received response: %s" (or response "nil"))

        (with-current-buffer (get-buffer ai-auto-complete-chat-buffer-name)
          (let ((inhibit-read-only t))
            ;; Remove the "Thinking..." text
            (goto-char (point-max))
            (delete-region (line-beginning-position) (point-max))

            ;; Handle different response cases
            (cond
             ;; Case 1: Response is nil (API error)
             ((null response)
              (insert (propertize "emacs: "
				  'face 'ai-auto-complete-system-face
				  'read-only t
				  'front-sticky t
				  'rear-nonsticky t))
              (insert (propertize "ERROR: Failed to get a response from the API. Please try again or switch to a different backend."
				  'face 'error
				  'read-only t
				  'front-sticky t
				  'rear-nonsticky t)))

             ;; Case 2: Response is an error message
             ((and (stringp response) (string-match-p "^ERROR:" response))
              (insert (propertize "emacs: "
				  'face 'ai-auto-complete-system-face
				  'read-only t
				  'front-sticky t
				  'rear-nonsticky t))
              (insert (propertize response
				  'face 'error
				  'read-only t
				  'front-sticky t
				  'rear-nonsticky t)))

             ;; Case 3: Normal response
             (t
              ;; Add the response to history
              (push (cons 'assistant response) ai-auto-complete--chat-history)
              ;; Mark session as modified
              (setq ai-auto-complete--session-modified t)
              ;; Insert the response with read-only property
              (insert (propertize ai-auto-complete-chat-response-prefix
                                  'face 'ai-auto-complete-assistant-face
                                  'read-only t
                                  'front-sticky t
                                  'rear-nonsticky t))
              (insert (propertize response
				  'read-only t
				  'front-sticky t
				  'rear-nonsticky t))))

            ;; Always add a new prompt, regardless of response type
            (insert (propertize "\n\n"
				'read-only t
				'front-sticky t
				'rear-nonsticky t))
            ;; Make the prompt prefix read-only but allow typing after it
            (insert (propertize ai-auto-complete-chat-prompt-prefix
				'face 'ai-auto-complete-user-face
				'read-only t
				'front-sticky t
				'rear-nonsticky t))
            (setq ai-auto-complete--chat-input-marker (point-marker))

            ;; Make sure the buffer is in a state where the user can type
            (setq buffer-read-only nil)

            ;; Always reset the in-progress flag
            (setq ai-auto-complete--chat-in-progress nil))))

    ;; Buffer not found case
    (message "Chat buffer not found, cannot display response")))

(defun ai-auto-complete-chat-clear ()
  "Clear the chat history and restart the conversation."
  (interactive)
  (when ai-auto-complete-chat-mode
    (when (and ai-auto-complete--session-modified
               (y-or-n-p "Current session has unsaved changes. Save before clearing? "))
      (call-interactively 'ai-auto-complete-chat-session-save))

    (when (y-or-n-p "Clear the current chat? ")
      (setq ai-auto-complete--chat-history nil)
      (setq ai-auto-complete--current-session-id nil)
      (setq ai-auto-complete--session-modified nil)
      ;; Note: We don't clear the shared context here, as it's shared across modes
      ;; Use ai-auto-complete-clear-context to clear the shared context
      (ai-auto-complete-chat-initialize))))

(defun ai-auto-complete-chat-cancel-request ()
  "Cancel the current chat request if one is in progress."
  (interactive)
  (when ai-auto-complete--chat-in-progress
    (message "Cancelling current chat request")
    (ai-auto-complete-chat-handle-response "ERROR: Request cancelled by user.")))

(defun ai-auto-complete-chat-display-tool-call (tool-name args)
  "Display a tool call in the chat buffer.
   TOOL-NAME is the name of the tool being called.
   ARGS is a plist of arguments for the tool."
  (let ((args-str ""))
    ;; Format the arguments
    (let ((arg-index 0))
      (while (< arg-index (length args))
        (let ((key (nth arg-index args))
              (value (nth (1+ arg-index) args)))
          (when (keywordp key)
            (setq args-str (concat args-str
                                   (format "\n  %s: %s"
                                           (substring (symbol-name key) 1)
                                           value))))
          (setq arg-index (+ arg-index 2)))))

    ;; Display the tool call
    (ai-auto-complete-chat-insert-message
     (format "%s%s" tool-name args-str)
     'tool)))

(defun ai-auto-complete-chat-display-tool-result (tool-name result)
  "Display a tool result in the chat buffer.
   TOOL-NAME is the name of the tool that was called.
   RESULT is the result returned by the tool."
  (ai-auto-complete-chat-insert-message
   (format "%s returned:\n%s" tool-name result)
   'tool-result))

(defun ai-auto-complete-chat-handle-agent-response (agent-name response)
  "Handle RESPONSE from AGENT-NAME in the chat buffer."
  (if (get-buffer ai-auto-complete-chat-buffer-name)
      (progn
        ;; Debug output
        (message "Received response from agent %s: %s" agent-name (or response "nil"))

        (with-current-buffer (get-buffer ai-auto-complete-chat-buffer-name)
          (let ((inhibit-read-only t))
            ;; Remove the "Thinking..." text
            (goto-char (point-max))
            (delete-region (line-beginning-position) (point-max))

            ;; Handle different response cases
            (cond
             ;; Case 1: Response is nil (API error)
             ((null response)
              (insert (propertize "emacs: "
				    'face 'ai-auto-complete-system-face
				    'read-only t
				    'front-sticky t
				    'rear-nonsticky t))
                (insert (propertize (format "ERROR: Failed to get a response from agent %s. Please try again." agent-name)
				    'face 'error
				    'read-only t
				    'front-sticky t
				    'rear-nonsticky t)))

             ;; Case 2: Response is an error message
             ((and (stringp response) (string-match-p "^ERROR:" response))
              (insert (propertize "emacs: "
				  'face 'ai-auto-complete-system-face
				  'read-only t
				  'front-sticky t
				  'rear-nonsticky t))
              (insert (propertize response
				  'face 'error
				  'read-only t
				  'front-sticky t
				  'rear-nonsticky t)))

             ;; Case 3: Normal response
             (t
              ;; Check if the response contains a message to another agent
              (if (and ai-auto-complete-agents-enabled
                       (string-match-p "^@[a-zA-Z0-9_-]+\\s-+" response))
                  (let ((agent-info (ai-auto-complete-message-to-agent response)))
                  (message "DEBUG-CHAT: Response contains message to another agent: %s" response)

                    (if agent-info
                        (let ((target-agent-name (car agent-info))
                              (agent-message (cdr agent-info)))
                          ;; Display the original agent's response
                          (insert (propertize (format "AGENT-%s: " agent-name)
                                              'face 'ai-auto-complete-agent-face
                                              'read-only t
                                              'front-sticky t
                                              'rear-nonsticky t))
                          (insert (propertize response
                                              'read-only t
                                              'front-sticky t
                                              'rear-nonsticky t))
                          ;; Process the message for the target agent
                          (insert (propertize "\n\n"
                                              'read-only t
                                              'front-sticky t
                                              'rear-nonsticky t))
                          (insert (propertize (format "@%s: " target-agent-name)
                                              'face 'ai-auto-complete-agent-face
                                              'read-only t
                                              'front-sticky t
                                              'rear-nonsticky t))
                          (insert (propertize "Thinking..."
                                              'face 'italic
                                              'read-only t
                                              'front-sticky t
                                              'rear-nonsticky t))
                          (setq ai-auto-complete--chat-in-progress t)
                          ;; Process the agent message
                          (message "DEBUG-CHAT: Processing message by agent %s for target agent %s: %s : message : %s" agent-name target-agent-name agent-message)
                         ; (message "DEBUG-CHAT: Current history has %d messages" (length ai-auto-complete--chat-history))

                          ;; Debug the history content
                          (let ((count 0))
                            (dolist (msg ai-auto-complete--chat-history)
                              (setq count (1+ count))
                              (message "DEBUG-CHAT: History item %d - Type: %s"
                                       count
                                       (type-of msg))

                              ;; Only process proper list items
                              (when (and msg (listp msg) (not (functionp msg)))
                                (condition-case err
                                    (let ((role (car msg))
                                          (content (cdr msg)))
                                      (message "DEBUG-CHAT: History item %d - Role: %s, Content: %s"
                                               count
                                               role
                                               (cond
                                                ;; Agent messages
                                                ((eq role 'agent)
                                                 (format "Agent %s: %s"
                                                         (car content)
                                                         (substring (format "%s" (cdr content))
                                                                    0
                                                                    (min 50 (length (format "%s" (cdr content)))))))
                                                ;; User messages
                                                ((eq role 'user)
                                                 (substring (format "%s" content)
                                                            0
                                                            (min 50 (length (format "%s" content)))))
                                                ;; Other message types
                                                (t (format "%s" content)))))
                                  (error
                                   (message "DEBUG-CHAT: Error processing history item %d: %s"
                                            count (error-message-string err)))))))

                          (ai-auto-complete-process-agent-message
                           target-agent-name
                           agent-message
                           ai-auto-complete--chat-history
                           (lambda (target-agent-name response)
                             ;; Add the response to history
                             (message "DEBUG-CHAT: Adding response from target agent %s to history" target-agent-name)
                             (push (cons 'agent (cons target-agent-name response)) ai-auto-complete--chat-history)
                             ;; Process the response for tools
                             (if (and (boundp 'ai-auto-complete-tools-enabled)
                                      ai-auto-complete-tools-enabled
                                      (fboundp 'ai-auto-complete-tools-process-response))
                                 (progn
                                   (message "DEBUG-CHAT: Processing response for tools")
                                   (ai-auto-complete-tools-process-response
                                    response
                                    (lambda (processed-response)
                                      (ai-auto-complete-chat-handle-agent-response target-agent-name processed-response))
                                    target-agent-name))
                               ;; If tools are not enabled, just handle the response directly
                               (ai-auto-complete-chat-handle-agent-response target-agent-name response)))))
                      ;; Invalid agent format
                      (progn
                        ;; Just display the original response
                        (insert (propertize (format "AGENT-%s: " agent-name)
                                            'face 'ai-auto-complete-agent-face
                                            'read-only t
                                            'front-sticky t
                                            'rear-nonsticky t))
                        (insert (propertize response
                                            'read-only t
                                            'front-sticky t
                                            'rear-nonsticky t)))))
                ;; response is not directed to any specific agent , so
                ;; we should be checking for tool calls now instead of starting another au-auto-complete-process-agent-message loop
                ;; Regular response with enhanced UI support
                (progn
                (message "DEBUG-CHAT: Processing regular response (not directed to any specific agent) from agent %s: %s" agent-name response)

                ;; Use streaming if enhanced UI is enabled
                (if ai-auto-complete-enhanced-ui-enabled
                    (progn
                      ;; Complete the streaming that was started earlier
                      (ai-auto-complete-streaming-simulate response 'agent agent-name))
                  ;; Fallback to regular display
                  ;; thread never reaches here in menu > enhanced UI > enable enhanced ui mode
                  (progn
                    (insert (propertize (format "AGENT-%s: " agent-name) ; When enhanced UI is disabled
                                        'face 'ai-auto-complete-agent-face ; Use standard agent face
                                        'read-only t
                                        'front-sticky t
                                        'rear-nonsticky t))
                    ;; Insert the raw response directly, as enhanced UI (and markdown) is disabled.
                    (insert (propertize response
                                         'read-only t
                                         'front-sticky t
                                         'rear-nonsticky t))))))))

            ;; Always add a new prompt, regardless of response type
            (insert (propertize "\n\n"
				'read-only t
				'front-sticky t
				'rear-nonsticky t))
            ;; Make the prompt prefix read-only but allow typing after it
            (insert (propertize ai-auto-complete-chat-prompt-prefix
				'face 'ai-auto-complete-user-face
				'read-only t
				'front-sticky t
				'rear-nonsticky t))
            (setq ai-auto-complete--chat-input-marker (point-marker))

            ;; Make sure the buffer is in a state where the user can type
            (setq buffer-read-only nil)

            ;; Always reset the in-progress flag
            (setq ai-auto-complete--chat-in-progress nil))))

    ;; Buffer not found case (else case)
    (message "Chat buffer not found, cannot display agent response")
    ))

(defun ai-auto-complete-chat-toggle-tool-results ()
  "Toggle display of tool results in the current chat buffer."
  (interactive)
  (setq ai-auto-complete--chat-show-tool-results
        (not ai-auto-complete--chat-show-tool-results))
  (message "Tool results %s"
           (if ai-auto-complete--chat-show-tool-results "enabled" "disabled"))
  (ai-auto-complete-chat-refresh))

(defun ai-auto-complete-chat-toggle-control-buttons ()
  "Toggle display of control buttons in the current chat buffer."
  (interactive)
  (setq ai-auto-complete-chat-show-control-buttons
        (not ai-auto-complete-chat-show-control-buttons))
  (message "Control buttons %s"
           (if ai-auto-complete-chat-show-control-buttons "enabled" "disabled"))
  (ai-auto-complete-chat-refresh))

(defun ai-auto-complete-chat-toggle-sidebar ()
  "Toggle the sidebar for agent management and context display."
  (interactive)
  (if (and (boundp 'ai-auto-complete-sidebar-window)
           ai-auto-complete-sidebar-window
           (window-live-p ai-auto-complete-sidebar-window))
      ;; Sidebar is visible, close it
      (progn
        (when (fboundp 'ai-auto-complete-sidebar-close)
          (ai-auto-complete-sidebar-close))
        (message "Sidebar closed"))
    ;; Sidebar is not visible, show it
    (when (fboundp 'ai-auto-complete-sidebar-show)
      (ai-auto-complete-sidebar-show)
      (message "Sidebar opened"))))

(defgroup ai-auto-complete-chat-features nil
  "Feature settings for AI Auto Complete chat."
  :group 'ai-auto-complete-chat
  :prefix "ai-auto-complete-chat-")

(defcustom ai-auto-complete-chat-default-streaming t
  "Whether to enable streaming by default in new chat buffers."
  :type 'boolean
  :group 'ai-auto-complete-chat-features)

(defcustom ai-auto-complete-chat-default-markdown t
  "Whether to enable markdown rendering by default in new chat buffers."
  :type 'boolean
  :group 'ai-auto-complete-chat-features)

(defcustom ai-auto-complete-chat-default-timestamps t
  "Whether to show timestamps by default in new chat buffers."
  :type 'boolean
  :group 'ai-auto-complete-chat-features)

(defcustom ai-auto-complete-chat-default-profile 'enhanced
  "Default profile for new chat buffers: 'enhanced, 'minimal, or 'custom."
  :type '(choice (const :tag "Enhanced" enhanced)
                 (const :tag "Minimal" minimal)
                 (const :tag "Custom" custom))
  :group 'ai-auto-complete-chat-features)

(defun ai-auto-complete-chat-set-minimal-profile ()
  "Set chat to minimal profile (basic features only)."
  (interactive)
  (setq ai-auto-complete-chat-streaming-enabled nil
        ai-auto-complete-chat-markdown-enabled nil
        ai-auto-complete-chat-show-timestamps nil)
  (ai-auto-complete-chat-refresh))

(defun ai-auto-complete-chat-set-enhanced-profile ()
  "Set chat to enhanced profile (all features enabled)."
  (interactive)
  (setq ai-auto-complete-chat-streaming-enabled t
        ai-auto-complete-chat-markdown-enabled t
        ai-auto-complete-chat-show-timestamps t)
  (ai-auto-complete-chat-refresh))

(setq ai-auto-complete-chat-default-streaming t)
(setq ai-auto-complete-chat-default-markdown t)
(setq ai-auto-complete-chat-default-timestamps t)
(setq ai-auto-complete-chat-default-tool-calls t)
(setq ai-auto-complete-chat-default-tool-results t)
(setq ai-auto-complete-chat-default-agent-controls t)
(setq ai-auto-complete-chat-default-control-buttons t)

;; Use enhanced profile (all features enabled)
(setq ai-auto-complete-chat-default-profile 'enhanced)

;; Use minimal profile (basic chat only)
(setq ai-auto-complete-chat-default-profile 'minimal)

;; Use custom profile with specific features
(setq ai-auto-complete-chat-default-profile 'custom
      ai-auto-complete-chat-default-streaming t
      ai-auto-complete-chat-default-markdown nil
      ai-auto-complete-chat-default-timestamps t)


(provide 'chat)
;;; chat.el ends here
